{"version": 3, "names": ["React", "ScrollView", "RNScrollView", "Switch", "RNSwitch", "TextInput", "RNTextInput", "DrawerLayoutAndroid", "RNDrawerLayoutAndroid", "FlatList", "RNFlatList", "RefreshControl", "RNRefreshControl", "createNativeWrapper", "nativeViewProps", "toArray", "jsx", "_jsx", "disallowInterruption", "shouldCancelWhenOutside", "GHScrollView", "forwardRef", "props", "ref", "refreshControlGestureRef", "useRef", "refreshControl", "waitFor", "rest", "cloneElement", "undefined", "shouldActivateOnStart", "flatListProps", "scrollViewProps", "propName", "value", "Object", "entries", "includes", "renderScrollComponent", "scrollProps"], "sourceRoot": "../../../src", "sources": ["components/GestureComponents.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAO9B,SACEC,UAAU,IAAIC,YAAY,EAE1BC,MAAM,IAAIC,QAAQ,EAElBC,SAAS,IAAIC,WAAW,EAExBC,mBAAmB,IAAIC,qBAAqB,EAE5CC,QAAQ,IAAIC,UAAU,EAEtBC,cAAc,IAAIC,gBAAgB,QAC7B,cAAc;AAErB,OAAOC,mBAAmB,MAAM,iCAAiC;AAEjE,SAEEC,eAAe,QACV,sCAAsC;AAE7C,SAASC,OAAO,QAAQ,UAAU;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAEnC,OAAO,MAAMN,cAAc,GAAGE,mBAAmB,CAACD,gBAAgB,EAAE;EAClEM,oBAAoB,EAAE,IAAI;EAC1BC,uBAAuB,EAAE;AAC3B,CAAC,CAAC;AACF;;AAGA,MAAMC,YAAY,GAAGP,mBAAmB,CACtCX,YAAY,EACZ;EACEgB,oBAAoB,EAAE,IAAI;EAC1BC,uBAAuB,EAAE;AAC3B,CACF,CAAC;AACD,OAAO,MAAMlB,UAAU,gBAAGD,KAAK,CAACqB,UAAU,CAGxC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAChB,MAAMC,wBAAwB,GAAGxB,KAAK,CAACyB,MAAM,CAAiB,IAAI,CAAC;EACnE,MAAM;IAAEC,cAAc;IAAEC,OAAO;IAAE,GAAGC;EAAK,CAAC,GAAGN,KAAK;EAElD,oBACEL,IAAA,CAACG,YAAY;IAAA,GACPQ,IAAI;IACR;IACAL,GAAG,EAAEA,GAAI;IACTI,OAAO,EAAE,CAAC,GAAGZ,OAAO,CAACY,OAAO,IAAI,EAAE,CAAC,EAAEH,wBAAwB;IAC7D;IAAA;IACAE,cAAc,EACZA,cAAc,gBACV1B,KAAK,CAAC6B,YAAY,CAACH,cAAc,EAAE;MACjC;MACAH,GAAG,EAAEC;IACP,CAAC,CAAC,GACFM;EACL,CACF,CAAC;AAEN,CAAC,CAAC;AACF;AACA;AACA;;AAGA,OAAO,MAAM3B,MAAM,GAAGU,mBAAmB,CAAgBT,QAAQ,EAAE;EACjEe,uBAAuB,EAAE,KAAK;EAC9BY,qBAAqB,EAAE,IAAI;EAC3Bb,oBAAoB,EAAE;AACxB,CAAC,CAAC;AACF;;AAGA,OAAO,MAAMb,SAAS,GAAGQ,mBAAmB,CAAmBP,WAAW,CAAC;AAC3E;;AAGA,OAAO,MAAMC,mBAAmB,GAAGM,mBAAmB,CAEpDL,qBAAqB,EAAE;EAAEU,oBAAoB,EAAE;AAAK,CAAC,CAAC;AACxD;;AAIA,OAAO,MAAMT,QAAQ,gBAAGT,KAAK,CAACqB,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACvD,MAAMC,wBAAwB,GAAGxB,KAAK,CAACyB,MAAM,CAAiB,IAAI,CAAC;EAEnE,MAAM;IAAEE,OAAO;IAAED,cAAc;IAAE,GAAGE;EAAK,CAAC,GAAGN,KAAK;EAElD,MAAMU,aAAa,GAAG,CAAC,CAAC;EACxB,MAAMC,eAAe,GAAG,CAAC,CAAC;EAC1B,KAAK,MAAM,CAACC,QAAQ,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACT,IAAI,CAAC,EAAE;IACpD;IACA,IAAKd,eAAe,CAAuBwB,QAAQ,CAACJ,QAAQ,CAAC,EAAE;MAC7D;MACA;MACAD,eAAe,CAACC,QAAQ,CAAC,GAAGC,KAAK;IACnC,CAAC,MAAM;MACL;MACA;MACAH,aAAa,CAACE,QAAQ,CAAC,GAAGC,KAAK;IACjC;EACF;EAEA;IAAA;IACE;IACAlB,IAAA,CAACP,UAAU;MACTa,GAAG,EAAEA,GAAI;MAAA,GACLS,aAAa;MACjBO,qBAAqB,EAAGC,WAAW,iBACjCvB,IAAA,CAAChB,UAAU;QAEP,GAAGuC,WAAW;QACd,GAAGP,eAAe;QAClBN,OAAO,EAAE,CAAC,GAAGZ,OAAO,CAACY,OAAO,IAAI,EAAE,CAAC,EAAEH,wBAAwB;MAAC,CAEjE;MAEH;MAAA;MACAE,cAAc,EACZA,cAAc,gBACV1B,KAAK,CAAC6B,YAAY,CAACH,cAAc,EAAE;QACjC;QACAH,GAAG,EAAEC;MACP,CAAC,CAAC,GACFM;IACL,CACF;EAAC;AAEN,CAAC,CAOuB;AACxB", "ignoreList": []}