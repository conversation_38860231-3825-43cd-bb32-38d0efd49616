{"version": 3, "names": ["_Gesture<PERSON><PERSON>ler", "_interopRequireDefault", "require", "_utils", "e", "__esModule", "default", "DiscreteGestureHandler", "Gesture<PERSON>andler", "isDiscrete", "shouldEnableGestureOnSetup", "shouldFailUnderCustomCriteria", "x", "y", "deltaX", "deltaY", "maxDeltaX", "maxDeltaY", "maxDistSq", "shouldCancelWhenOutside", "isPointInView", "TEST_MAX_IF_NOT_NAN", "Math", "abs", "transformNativeEvent", "center", "rect", "view", "getBoundingClientRect", "absoluteX", "absoluteY", "left", "top", "isGestureEnabledForEvent", "minPointers", "maxPointers", "_recognizer", "pointer<PERSON><PERSON><PERSON>", "validPointerCount", "isGestureRunning", "failed", "success", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["web_hammer/DiscreteGestureHandler.ts"], "mappings": ";;;;;;AAEA,IAAAA,eAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAA8C,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAH9C;AACA;;AAIA,MAAeG,sBAAsB,SAASC,uBAAc,CAAC;EAC3D,IAAIC,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI;EACb;EAEA,IAAIC,0BAA0BA,CAAA,EAAG;IAC/B,OAAO,IAAI;EACb;EAEAC,6BAA6BA,CAC3B;IAAEC,CAAC;IAAEC,CAAC;IAAEC,MAAM;IAAEC;EAAY,CAAC,EAC7B;IAAEC,SAAS;IAAEC,SAAS;IAAEC,SAAS;IAAEC;EAA6B,CAAC,EACjE;IACA,IAAIA,uBAAuB,EAAE;MAC3B,IAAI,CAAC,IAAI,CAACC,aAAa,CAAC;QAAER,CAAC;QAAEC;MAAE,CAAC,CAAC,EAAE;QACjC,OAAO,IAAI;MACb;IACF;IACA,OACE,IAAAQ,0BAAmB,EAACC,IAAI,CAACC,GAAG,CAACT,MAAM,CAAC,EAAEE,SAAS,CAAC,IAChD,IAAAK,0BAAmB,EAACC,IAAI,CAACC,GAAG,CAACR,MAAM,CAAC,EAAEE,SAAS,CAAC,IAChD,IAAAI,0BAAmB,EACjBC,IAAI,CAACC,GAAG,CAACR,MAAM,GAAGA,MAAM,GAAGD,MAAM,GAAGA,MAAM,CAAC,EAC3CI,SACF,CAAC;EAEL;EAEAM,oBAAoBA,CAAC;IAAEC,MAAM,EAAE;MAAEb,CAAC;MAAEC;IAAE;EAAO,CAAC,EAAE;IAC9C;IACA,MAAMa,IAAI,GAAG,IAAI,CAACC,IAAI,CAAEC,qBAAqB,CAAC,CAAC;IAE/C,OAAO;MACLC,SAAS,EAAEjB,CAAC;MACZkB,SAAS,EAAEjB,CAAC;MACZD,CAAC,EAAEA,CAAC,GAAGc,IAAI,CAACK,IAAI;MAChBlB,CAAC,EAAEA,CAAC,GAAGa,IAAI,CAACM;IACd,CAAC;EACH;EAEAC,wBAAwBA,CACtB;IACEC,WAAW;IACXC,WAAW;IACXnB,SAAS;IACTC,SAAS;IACTC,SAAS;IACTC;EACG,CAAC,EACNiB,WAAgB,EAChB;IAAED,WAAW,EAAEE,aAAa;IAAEZ,MAAM;IAAEX,MAAM;IAAEC;EAAY,CAAC,EAC3D;IACA,MAAMuB,iBAAiB,GACrBD,aAAa,IAAIH,WAAW,IAAIG,aAAa,IAAIF,WAAW;IAE9D,IACE,IAAI,CAACxB,6BAA6B,CAChC;MAAE,GAAGc,MAAM;MAAEX,MAAM;MAAEC;IAAO,CAAC,EAC7B;MACEC,SAAS;MACTC,SAAS;MACTC,SAAS;MACTC;IACF,CACF,CAAC;IACD;IACA;IACC,CAACmB,iBAAiB,IAAI,IAAI,CAACC,gBAAiB,EAC7C;MACA,OAAO;QAAEC,MAAM,EAAE;MAAK,CAAC;IACzB;IAEA,OAAO;MAAEC,OAAO,EAAEH;IAAkB,CAAC;EACvC;AACF;AAAC,IAAAI,QAAA,GAAAC,OAAA,CAAArC,OAAA,GAEcC,sBAAsB", "ignoreList": []}