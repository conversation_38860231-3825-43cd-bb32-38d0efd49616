{"version": 3, "names": ["_reactNative", "require", "_utils", "findNodeHandle", "viewRef", "FlatList", "_listRef", "_scrollRef", "<PERSON><PERSON><PERSON><PERSON>", "viewTag", "undefined", "Element", "style", "display", "isRNSVGElement", "elementRef", "current", "element"], "sourceRoot": "../../src", "sources": ["findNodeHandle.web.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AAEe,SAASE,cAAcA,CACpCC,OAA8D,EAC3B;EACnC;EACA,IAAIA,OAAO,YAAYC,qBAAQ,EAAE;IAC/B;IACA,OAAOD,OAAO,CAACE,QAAQ,CAACC,UAAU,CAACC,UAAU;EAC/C;EACA;EACA;EACA;EACA,IAAKJ,OAAO,EAAwBK,OAAO,KAAKC,SAAS,EAAE;IACzD,OAAOP,cAAc,CAAEC,OAAO,CAAuBK,OAAO,CAAC;EAC/D;EAEA,IAAIL,OAAO,YAAYO,OAAO,EAAE;IAC9B,IAAIP,OAAO,CAACQ,KAAK,CAACC,OAAO,KAAK,UAAU,EAAE;MACxC,OAAOV,cAAc,CAACC,OAAO,CAACI,UAAyB,CAAC;IAC1D;IAEA,OAAOJ,OAAO;EAChB;EAEA,IAAI,IAAAU,qBAAc,EAACV,OAAO,CAAC,EAAE;IAC3B,OAAQA,OAAO,CAAYW,UAAU,CAACC,OAAO;EAC/C;;EAEA;EACA;EACA,IAAIC,OAAO,GAAIb,OAAO,EAAwBY,OAAO;EAErD,OAAOC,OAAO,IAAIA,OAAO,CAACL,KAAK,CAACC,OAAO,KAAK,UAAU,EAAE;IACtDI,OAAO,GAAGA,OAAO,CAACT,UAAyB;EAC7C;EAEA,OAAOS,OAAO;AAChB", "ignoreList": []}