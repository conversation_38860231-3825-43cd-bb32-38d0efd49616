/**
 * Performance Monitoring Service
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import { Logger } from './Logger';

// Performance metric types
export interface PerformanceMetric {
  id: string;
  name: string;
  category: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
  timestamp: string;
}

// Memory usage information
export interface MemoryInfo {
  used: number;
  total: number;
  available: number;
  percentage: number;
  timestamp: string;
}

// Render performance metrics
export interface RenderMetric {
  componentName: string;
  renderTime: number;
  propsCount: number;
  reRenderCount: number;
  timestamp: string;
}

// Performance thresholds
export interface PerformanceThresholds {
  slowRender: number; // ms
  memoryWarning: number; // percentage
  memoryCritical: number; // percentage
  slowOperation: number; // ms
}

// Performance monitoring configuration
export interface PerformanceConfig {
  enabled: boolean;
  enableMemoryMonitoring: boolean;
  enableRenderMonitoring: boolean;
  enableOperationMonitoring: boolean;
  sampleRate: number; // 0-1
  maxMetrics: number;
  thresholds: PerformanceThresholds;
}

// Default configuration
const DEFAULT_CONFIG: PerformanceConfig = {
  enabled: true,
  enableMemoryMonitoring: true,
  enableRenderMonitoring: __DEV__,
  enableOperationMonitoring: true,
  sampleRate: __DEV__ ? 1.0 : 0.1,
  maxMetrics: 1000,
  thresholds: {
    slowRender: 16, // 60fps = 16.67ms per frame
    memoryWarning: 80,
    memoryCritical: 95,
    slowOperation: 1000,
  },
};

class PerformanceMonitorService {
  private config: PerformanceConfig;
  private metrics: PerformanceMetric[] = [];
  private memoryHistory: MemoryInfo[] = [];
  private renderMetrics: RenderMetric[] = [];
  private activeOperations: Map<string, PerformanceMetric> = new Map();
  private memoryMonitorInterval?: NodeJS.Timeout;
  private isInitialized = false;

  constructor(config: Partial<PerformanceConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  // Initialize performance monitoring
  initialize(): void {
    if (this.isInitialized || !this.config.enabled) return;

    Logger.info('Performance monitoring initialized', 'performance', {
      config: this.config,
    });

    // Start memory monitoring
    if (this.config.enableMemoryMonitoring) {
      this.startMemoryMonitoring();
    }

    this.isInitialized = true;
  }

  // Start memory monitoring
  private startMemoryMonitoring(): void {
    this.memoryMonitorInterval = setInterval(() => {
      this.captureMemoryUsage();
    }, 5000); // Check every 5 seconds
  }

  // Capture current memory usage
  private captureMemoryUsage(): void {
    try {
      // React Native doesn't have direct memory access
      // This would need to be implemented with native modules
      // For now, we'll simulate memory monitoring
      const memoryInfo: MemoryInfo = {
        used: 0,
        total: 0,
        available: 0,
        percentage: 0,
        timestamp: new Date().toISOString(),
      };

      this.memoryHistory.push(memoryInfo);

      // Keep only recent memory data
      if (this.memoryHistory.length > 100) {
        this.memoryHistory.shift();
      }

      // Check thresholds
      if (memoryInfo.percentage > this.config.thresholds.memoryCritical) {
        Logger.error('Critical memory usage detected', 'performance', memoryInfo);
      } else if (memoryInfo.percentage > this.config.thresholds.memoryWarning) {
        Logger.warn('High memory usage detected', 'performance', memoryInfo);
      }
    } catch (error) {
      Logger.error('Failed to capture memory usage', 'performance', {}, error as Error);
    }
  }

  // Start timing an operation
  startTiming(name: string, category = 'operation', metadata?: Record<string, any>): string {
    if (!this.shouldSample()) return '';

    const id = this.generateMetricId();
    const metric: PerformanceMetric = {
      id,
      name,
      category,
      startTime: performance.now(),
      metadata,
      timestamp: new Date().toISOString(),
    };

    this.activeOperations.set(id, metric);
    return id;
  }

  // End timing an operation
  endTiming(id: string): PerformanceMetric | null {
    if (!id || !this.activeOperations.has(id)) return null;

    const metric = this.activeOperations.get(id)!;
    metric.endTime = performance.now();
    metric.duration = metric.endTime - metric.startTime;

    this.activeOperations.delete(id);
    this.addMetric(metric);

    // Check for slow operations
    if (metric.duration > this.config.thresholds.slowOperation) {
      Logger.warn('Slow operation detected', 'performance', {
        name: metric.name,
        duration: metric.duration,
        category: metric.category,
      });
    }

    return metric;
  }

  // Measure a function execution time
  async measureAsync<T>(
    name: string,
    fn: () => Promise<T>,
    category = 'async_operation',
    metadata?: Record<string, any>
  ): Promise<T> {
    const id = this.startTiming(name, category, metadata);
    try {
      const result = await fn();
      this.endTiming(id);
      return result;
    } catch (error) {
      this.endTiming(id);
      throw error;
    }
  }

  // Measure synchronous function execution time
  measure<T>(
    name: string,
    fn: () => T,
    category = 'sync_operation',
    metadata?: Record<string, any>
  ): T {
    const id = this.startTiming(name, category, metadata);
    try {
      const result = fn();
      this.endTiming(id);
      return result;
    } catch (error) {
      this.endTiming(id);
      throw error;
    }
  }

  // Record render performance
  recordRender(
    componentName: string,
    renderTime: number,
    propsCount = 0,
    reRenderCount = 1
  ): void {
    if (!this.config.enableRenderMonitoring || !this.shouldSample()) return;

    const renderMetric: RenderMetric = {
      componentName,
      renderTime,
      propsCount,
      reRenderCount,
      timestamp: new Date().toISOString(),
    };

    this.renderMetrics.push(renderMetric);

    // Keep only recent render metrics
    if (this.renderMetrics.length > this.config.maxMetrics) {
      this.renderMetrics.shift();
    }

    // Check for slow renders
    if (renderTime > this.config.thresholds.slowRender) {
      Logger.warn('Slow render detected', 'performance', {
        componentName,
        renderTime,
        propsCount,
        reRenderCount,
      });
    }
  }

  // Add a performance metric
  private addMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);

    // Keep only recent metrics
    if (this.metrics.length > this.config.maxMetrics) {
      this.metrics.shift();
    }

    Logger.debug('Performance metric recorded', 'performance', {
      name: metric.name,
      duration: metric.duration,
      category: metric.category,
    });
  }

  // Check if we should sample this metric
  private shouldSample(): boolean {
    return Math.random() < this.config.sampleRate;
  }

  // Generate unique metric ID
  private generateMetricId(): string {
    return `metric_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Get performance statistics
  getStats(): {
    totalMetrics: number;
    averageDuration: number;
    slowOperations: number;
    memoryUsage: MemoryInfo | null;
    renderStats: {
      totalRenders: number;
      averageRenderTime: number;
      slowRenders: number;
    };
  } {
    const durations = this.metrics
      .filter(m => m.duration !== undefined)
      .map(m => m.duration!);

    const slowOperations = durations.filter(
      d => d > this.config.thresholds.slowOperation
    ).length;

    const slowRenders = this.renderMetrics.filter(
      r => r.renderTime > this.config.thresholds.slowRender
    ).length;

    const averageDuration = durations.length > 0
      ? durations.reduce((sum, d) => sum + d, 0) / durations.length
      : 0;

    const averageRenderTime = this.renderMetrics.length > 0
      ? this.renderMetrics.reduce((sum, r) => sum + r.renderTime, 0) / this.renderMetrics.length
      : 0;

    return {
      totalMetrics: this.metrics.length,
      averageDuration,
      slowOperations,
      memoryUsage: this.memoryHistory[this.memoryHistory.length - 1] || null,
      renderStats: {
        totalRenders: this.renderMetrics.length,
        averageRenderTime,
        slowRenders,
      },
    };
  }

  // Get metrics by category
  getMetricsByCategory(category: string): PerformanceMetric[] {
    return this.metrics.filter(m => m.category === category);
  }

  // Get recent metrics
  getRecentMetrics(limit = 50): PerformanceMetric[] {
    return this.metrics.slice(-limit);
  }

  // Get memory history
  getMemoryHistory(): MemoryInfo[] {
    return [...this.memoryHistory];
  }

  // Get render metrics
  getRenderMetrics(): RenderMetric[] {
    return [...this.renderMetrics];
  }

  // Clear all metrics
  clearMetrics(): void {
    this.metrics = [];
    this.memoryHistory = [];
    this.renderMetrics = [];
    this.activeOperations.clear();
    Logger.info('Performance metrics cleared', 'performance');
  }

  // Update configuration
  updateConfig(newConfig: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...newConfig };
    Logger.info('Performance monitoring configuration updated', 'performance', newConfig);
  }

  // Cleanup
  destroy(): void {
    if (this.memoryMonitorInterval) {
      clearInterval(this.memoryMonitorInterval);
    }
    this.clearMetrics();
    this.isInitialized = false;
    Logger.info('Performance monitoring destroyed', 'performance');
  }
}

// Create and export singleton instance
export const PerformanceMonitor = new PerformanceMonitorService();

// Export convenience functions
export const startTiming = (name: string, category?: string, metadata?: Record<string, any>) =>
  PerformanceMonitor.startTiming(name, category, metadata);

export const endTiming = (id: string) => PerformanceMonitor.endTiming(id);

export const measureAsync = <T>(
  name: string,
  fn: () => Promise<T>,
  category?: string,
  metadata?: Record<string, any>
) => PerformanceMonitor.measureAsync(name, fn, category, metadata);

export const measure = <T>(
  name: string,
  fn: () => T,
  category?: string,
  metadata?: Record<string, any>
) => PerformanceMonitor.measure(name, fn, category, metadata);

export default PerformanceMonitor;
