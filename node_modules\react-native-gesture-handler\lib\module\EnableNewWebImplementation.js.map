{"version": 3, "names": ["Platform", "tagMessage", "useNewWebImplementation", "getWasCalled", "enableExperimentalWebImplementation", "_shouldEnable", "console", "warn", "enableLegacyWebImplementation", "shouldUseLegacyImplementation", "OS", "error", "isNewWebImplementationEnabled"], "sourceRoot": "../../src", "sources": ["EnableNewWebImplementation.ts"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,cAAc;AACvC,SAASC,UAAU,QAAQ,SAAS;AAEpC,IAAIC,uBAAuB,GAAG,IAAI;AAClC,IAAIC,YAAY,GAAG,KAAK;;AAExB;AACA;AACA;AACA,OAAO,SAASC,mCAAmCA,CACjDC,aAAa,GAAG,IAAI,EACd;EACN;EACAC,OAAO,CAACC,IAAI,CACVN,UAAU,CACR,mGACF,CACF,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAASO,6BAA6BA,CAC3CC,6BAA6B,GAAG,IAAI,EAC9B;EACNH,OAAO,CAACC,IAAI,CACVN,UAAU,CACR,8FACF,CACF,CAAC;EAED,IACED,QAAQ,CAACU,EAAE,KAAK,KAAK,IACrBR,uBAAuB,KAAK,CAACO,6BAA6B,EAC1D;IACA;EACF;EAEA,IAAIN,YAAY,EAAE;IAChBG,OAAO,CAACK,KAAK,CACX,mLACF,CAAC;IACD;EACF;EAEAT,uBAAuB,GAAG,CAACO,6BAA6B;AAC1D;AAEA,OAAO,SAASG,6BAA6BA,CAAA,EAAY;EACvDT,YAAY,GAAG,IAAI;EACnB,OAAOD,uBAAuB;AAChC", "ignoreList": []}