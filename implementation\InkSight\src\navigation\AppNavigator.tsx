/**
 * Main App Navigator
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
// import { createDrawerNavigator } from '@react-navigation/drawer';

// Import navigation types
import {
  RootStackParamList,
  MainTabParamList,
  LibraryStackParamList,
  NotesStackParamList,
} from './types';

// Import screens (placeholder components for now)
import WelcomeScreen from '../screens/WelcomeScreen';
import LibraryScreen from '../screens/LibraryScreen';
import NotesScreen from '../screens/NotesScreen';
import RecentScreen from '../screens/RecentScreen';
import SettingsScreen from '../screens/SettingsScreen';

// Create navigators
const RootStack = createStackNavigator<RootStackParamList>();
const MainTab = createBottomTabNavigator<MainTabParamList>();
const LibraryStack = createStackNavigator<LibraryStackParamList>();
const NotesStack = createStackNavigator<NotesStackParamList>();

// Library Stack Navigator
const LibraryStackNavigator = () => {
  return (
    <LibraryStack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#6200EE',
        },
        headerTintColor: '#FFFFFF',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <LibraryStack.Screen
        name="LibraryHome"
        component={LibraryScreen}
        options={{ title: 'Library' }}
      />
    </LibraryStack.Navigator>
  );
};

// Notes Stack Navigator
const NotesStackNavigator = () => {
  return (
    <NotesStack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#6200EE',
        },
        headerTintColor: '#FFFFFF',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <NotesStack.Screen
        name="NotesHome"
        component={NotesScreen}
        options={{ title: 'Notes' }}
      />
    </NotesStack.Navigator>
  );
};

// Main Tab Navigator
const MainTabNavigator = () => {
  return (
    <MainTab.Navigator
      screenOptions={{
        tabBarActiveTintColor: '#6200EE',
        tabBarInactiveTintColor: '#757575',
        tabBarStyle: {
          backgroundColor: '#FFFFFF',
          borderTopColor: '#E0E0E0',
          borderTopWidth: 1,
        },
        headerShown: false,
      }}
    >
      <MainTab.Screen
        name="Library"
        component={LibraryStackNavigator}
        options={{
          title: 'Library',
          tabBarIcon: ({ color: _color, size: _size }) => (
            // TODO: Replace with proper icon component
            <></>
          ),
        }}
      />
      <MainTab.Screen
        name="Notes"
        component={NotesStackNavigator}
        options={{
          title: 'Notes',
          tabBarIcon: ({ color, size }) => (
            // TODO: Replace with proper icon component
            <></>
          ),
        }}
      />
      <MainTab.Screen
        name="Recent"
        component={RecentScreen}
        options={{
          title: 'Recent',
          tabBarIcon: ({ color, size }) => (
            // TODO: Replace with proper icon component
            <></>
          ),
        }}
      />
      <MainTab.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          title: 'Settings',
          tabBarIcon: ({ color, size }) => (
            // TODO: Replace with proper icon component
            <></>
          ),
        }}
      />
    </MainTab.Navigator>
  );
};

// Root Stack Navigator
const RootStackNavigator = () => {
  return (
    <RootStack.Navigator
      initialRouteName="Welcome"
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
        cardStyleInterpolator: ({ current, layouts }) => {
          return {
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [layouts.screen.width, 0],
                  }),
                },
              ],
            },
          };
        },
      }}
    >
      <RootStack.Screen
        name="Welcome"
        component={WelcomeScreen}
        options={{ title: 'Welcome to InkSight' }}
      />
      <RootStack.Screen
        name="MainTabs"
        component={MainTabNavigator}
        options={{ title: 'InkSight' }}
      />
    </RootStack.Navigator>
  );
};

// Deep linking configuration
const linking = {
  prefixes: ['inksight://'],
  config: {
    screens: {
      Welcome: 'welcome',
      MainTabs: 'main',
    },
  },
};

// Main App Navigator Component
const AppNavigator: React.FC = () => {
  return (
    <NavigationContainer linking={linking}>
      <RootStackNavigator />
    </NavigationContainer>
  );
};

export default AppNavigator;
