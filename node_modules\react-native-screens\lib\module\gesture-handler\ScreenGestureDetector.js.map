{"version": 3, "names": ["React", "useEffect", "Dimensions", "Platform", "findNodeHandle", "GestureDetector", "Gesture", "useSharedValue", "measure", "startScreenTransition", "finishScreenTransition", "makeMutable", "runOnUI", "getShadowNodeWrapperAndTagFromRef", "isF<PERSON><PERSON>", "RNScreensTurboModule", "DefaultEvent", "DefaultScreenDimensions", "checkBoundaries", "checkIfTransitionCancelled", "getAnimationForTransition", "EmptyGestureHandler", "Fling", "enabled", "ScreenGestureDetector", "children", "gestureDetectorBridge", "goBackGesture", "screenEdgeGesture", "transitionAnimation", "customTransitionAnimation", "screensRefs", "currentScreenId", "sharedEvent", "startingGesturePosition", "canPerformUpdates", "screenTransitionConfig", "stackTag", "belowTopScreenId", "topScreenId", "screenTransition", "isTransitionCanceled", "screenDimensions", "onFinishAnimation", "screenTagToNodeWrapperUI", "IS_FABRIC", "current", "stackUseEffectCallback", "stackRef", "value", "OS", "disableSwipeBackForTopScreen", "undefined", "screenTagToNodeWrapper", "key", "screenRef", "screenData", "tag", "shadowNodeWrapper", "console", "warn", "computeProgress", "event", "progress", "startingPosition", "translationX", "width", "absoluteX", "translationY", "height", "absoluteY", "Math", "abs", "progressX", "progressY", "max", "onStart", "transitionConfig", "transitionData", "startTransition", "canStartTransition", "topScreenTag", "belowTopScreenTag", "animatedRefMock", "screenSize", "Error", "finishTransition", "onUpdate", "updateTransition", "onEnd", "velocityFactor", "distanceX", "min", "velocityX", "distanceY", "velocityY", "requiredXDistance", "requiredYDistance", "panGesture", "Pan", "HIT_SLOP_SIZE", "ACTIVATION_DISTANCE", "activeOffsetX", "hitSlop", "left", "top", "right", "activeOffsetY", "get", "bottom", "createElement", "gesture"], "sourceRoot": "../../../src", "sources": ["gesture-handler/ScreenGestureDetector.tsx"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,UAAU,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,cAAc;AACnE,SACEC,eAAe,EACfC,OAAO,QAGF,8BAA8B;AACrC,SACEC,cAAc,EACdC,OAAO,EACPC,qBAAqB,EACrBC,sBAAsB,EACtBC,WAAW,EACXC,OAAO,QACF,yBAAyB;AAChC,SAASC,iCAAiC,EAAEC,QAAQ,QAAQ,eAAe;AAC3E,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,YAAY,EAAEC,uBAAuB,QAAQ,YAAY;AAClE,SACEC,eAAe,EACfC,0BAA0B,EAC1BC,yBAAyB,QACpB,eAAe;AAGtB;AACA;AACA,MAAMC,mBAAmB,GAAGf,OAAO,CAACgB,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,KAAK,CAAC;AAE1D,MAAMC,qBAAqB,GAAGA,CAAC;EAC7BC,QAAQ;EACRC,qBAAqB;EACrBC,aAAa;EACbC,iBAAiB;EACjBC,mBAAmB,EAAEC,yBAAyB;EAC9CC,WAAW;EACXC;AACoB,CAAC,KAAK;EAC1B,MAAMC,WAAW,GAAG1B,cAAc,CAACS,YAAY,CAAC;EAChD,MAAMkB,uBAAuB,GAAG3B,cAAc,CAACS,YAAY,CAAC;EAC5D,MAAMmB,iBAAiB,GAAGxB,WAAW,CAAC,KAAK,CAAC;EAC5C,MAAMkB,mBAAmB,GAAGT,yBAAyB,CACnDO,aAAa,EACbG,yBACF,CAAC;EACD,MAAMM,sBAAsB,GAAGzB,WAAW,CAAC;IACzC0B,QAAQ,EAAE,CAAC,CAAC;IACZC,gBAAgB,EAAE,CAAC,CAAC;IACpBC,WAAW,EAAE,CAAC,CAAC;IACfN,WAAW;IACXC,uBAAuB;IACvBM,gBAAgB,EAAEX,mBAAmB;IACrCY,oBAAoB,EAAE,KAAK;IAC3Bd,aAAa,EAAEA,aAAa,IAAI,YAAY;IAC5Ce,gBAAgB,EAAEzB,uBAAuB;IACzC0B,iBAAiB,EAAEA,CAAA,KAAM;MACvB,SAAS;IACX;EACF,CAAC,CAAC;EACF,MAAMN,QAAQ,GAAG1B,WAAW,CAAC,CAAC,CAAC,CAAC;EAChC,MAAMiC,wBAAwB,GAAGjC,WAAW,CAAsB,CAAC,CAAC,CAAC;EACrE,MAAMkC,SAAS,GAAG/B,QAAQ,CAAC,CAAC;EAE5BY,qBAAqB,CAACoB,OAAO,CAACC,sBAAsB,GAAGC,QAAQ,IAAI;IACjE,IAAI,CAACrB,aAAa,EAAE;MAClB;IACF;IACAU,QAAQ,CAACY,KAAK,GAAG7C,cAAc,CAAC4C,QAAQ,CAACF,OAAc,CAAW;IAClE,IAAI3C,QAAQ,CAAC+C,EAAE,KAAK,KAAK,EAAE;MACzBtC,OAAO,CAAC,MAAM;QACZG,oBAAoB,CAACoC,4BAA4B,CAACd,QAAQ,CAACY,KAAK,CAAC;MACnE,CAAC,CAAC,CAAC,CAAC;IACN;EACF,CAAC;EAEDhD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4C,SAAS,IAAI,CAAClB,aAAa,IAAII,WAAW,KAAKqB,SAAS,EAAE;MAC7D;IACF;IACA,MAAMC,sBAA+D,GAAG,CAAC,CAAC;IAC1E,KAAK,MAAMC,GAAG,IAAIvB,WAAW,CAACe,OAAO,EAAE;MACrC,MAAMS,SAAS,GAAGxB,WAAW,CAACe,OAAO,CAACQ,GAAG,CAAC;MAC1C,MAAME,UAAU,GAAG3C,iCAAiC,CAAC0C,SAAS,CAACT,OAAO,CAAC;MACvE,IAAIU,UAAU,CAACC,GAAG,IAAID,UAAU,CAACE,iBAAiB,EAAE;QAClDL,sBAAsB,CAACG,UAAU,CAACC,GAAG,CAAC,GAAGD,UAAU,CAACE,iBAAiB;MACvE,CAAC,MAAM;QACLC,OAAO,CAACC,IAAI,CAAC,4CAA4C,CAAC;MAC5D;IACF;IACAhB,wBAAwB,CAACK,KAAK,GAAGI,sBAAsB;EACzD,CAAC,EAAE,CAACrB,eAAe,EAAEL,aAAa,CAAC,CAAC;EAEpC,SAASkC,eAAeA,CACtBC,KAAwD,EACxD;IACA,SAAS;;IACT,IAAIC,QAAQ,GAAG,CAAC;IAChB,MAAMrB,gBAAgB,GAAGN,sBAAsB,CAACa,KAAK,CAACP,gBAAgB;IACtE,MAAMsB,gBAAgB,GAAG9B,uBAAuB,CAACe,KAAK;IACtD,IAAItB,aAAa,KAAK,YAAY,EAAE;MAClCoC,QAAQ,GACND,KAAK,CAACG,YAAY,IACjBvB,gBAAgB,CAACwB,KAAK,GAAGF,gBAAgB,CAACG,SAAS,CAAC;IACzD,CAAC,MAAM,IAAIxC,aAAa,KAAK,WAAW,EAAE;MACxCoC,QAAQ,GAAI,CAAC,CAAC,GAAGD,KAAK,CAACG,YAAY,GAAID,gBAAgB,CAACG,SAAS;IACnE,CAAC,MAAM,IAAIxC,aAAa,KAAK,WAAW,EAAE;MACxCoC,QAAQ,GACL,CAAC,CAAC,GAAGD,KAAK,CAACM,YAAY,IACvB1B,gBAAgB,CAAC2B,MAAM,GAAGL,gBAAgB,CAACM,SAAS,CAAC;IAC1D,CAAC,MAAM,IAAI3C,aAAa,KAAK,SAAS,EAAE;MACtCoC,QAAQ,GAAGD,KAAK,CAACM,YAAY,GAAGJ,gBAAgB,CAACM,SAAS;IAC5D,CAAC,MAAM,IAAI3C,aAAa,KAAK,iBAAiB,EAAE;MAC9CoC,QAAQ,GAAGQ,IAAI,CAACC,GAAG,CAACV,KAAK,CAACG,YAAY,GAAGvB,gBAAgB,CAACwB,KAAK,GAAG,CAAC,CAAC;IACtE,CAAC,MAAM,IAAIvC,aAAa,KAAK,eAAe,EAAE;MAC5CoC,QAAQ,GAAGQ,IAAI,CAACC,GAAG,CAACV,KAAK,CAACM,YAAY,GAAG1B,gBAAgB,CAAC2B,MAAM,GAAG,CAAC,CAAC;IACvE,CAAC,MAAM,IAAI1C,aAAa,KAAK,qBAAqB,EAAE;MAClD,MAAM8C,SAAS,GAAGF,IAAI,CAACC,GAAG,CACxBV,KAAK,CAACG,YAAY,GAAGvB,gBAAgB,CAACwB,KAAK,GAAG,CAChD,CAAC;MACD,MAAMQ,SAAS,GAAGH,IAAI,CAACC,GAAG,CACxBV,KAAK,CAACM,YAAY,GAAG1B,gBAAgB,CAAC2B,MAAM,GAAG,CACjD,CAAC;MACDN,QAAQ,GAAGQ,IAAI,CAACI,GAAG,CAACF,SAAS,EAAEC,SAAS,CAAC;IAC3C;IACA,OAAOX,QAAQ;EACjB;EAEA,SAASa,OAAOA,CAACd,KAAwD,EAAE;IACzE,SAAS;;IACT7B,WAAW,CAACgB,KAAK,GAAGa,KAAK;IACzB,MAAMe,gBAAgB,GAAGzC,sBAAsB,CAACa,KAAK;IACrD,MAAM6B,cAAc,GAAG/D,oBAAoB,CAACgE,eAAe,CAAC1C,QAAQ,CAACY,KAAK,CAAC;IAC3E,IAAI6B,cAAc,CAACE,kBAAkB,KAAK,KAAK,EAAE;MAC/C7C,iBAAiB,CAACc,KAAK,GAAG,KAAK;MAC/B;IACF;IAEA,IAAIJ,SAAS,EAAE;MACbgC,gBAAgB,CAACtC,WAAW,GAC1BK,wBAAwB,CAACK,KAAK,CAAC6B,cAAc,CAACG,YAAY,CAAC;MAC7DJ,gBAAgB,CAACvC,gBAAgB,GAC/BM,wBAAwB,CAACK,KAAK,CAAC6B,cAAc,CAACI,iBAAiB,CAAC;IACpE,CAAC,MAAM;MACLL,gBAAgB,CAACtC,WAAW,GAAGuC,cAAc,CAACG,YAAY;MAC1DJ,gBAAgB,CAACvC,gBAAgB,GAAGwC,cAAc,CAACI,iBAAiB;IACtE;IAEAL,gBAAgB,CAACxC,QAAQ,GAAGA,QAAQ,CAACY,KAAK;IAC1Cf,uBAAuB,CAACe,KAAK,GAAGa,KAAK;IACrC,MAAMqB,eAAe,GAAGA,CAAA,KAAM;MAC5B,OAAO/C,sBAAsB,CAACa,KAAK,CAACV,WAAW;IACjD,CAAC;IACD,MAAM6C,UAAU,GAAG5E,OAAO,CAAC2E,eAAsB,CAAC;IAClD,IAAIC,UAAU,IAAI,IAAI,EAAE;MACtB,MAAM,IAAIC,KAAK,CAAC,uCAAuC,CAAC;IAC1D;IACA,IAAID,UAAU,IAAI,IAAI,EAAE;MACtBjD,iBAAiB,CAACc,KAAK,GAAG,KAAK;MAC/BlC,oBAAoB,CAACuE,gBAAgB,CAACjD,QAAQ,CAACY,KAAK,EAAE,IAAI,CAAC;MAC3D;IACF;IACA4B,gBAAgB,CAACnC,gBAAgB,GAAG0C,UAAU;IAC9C;IACA;IACA;IACA;IACA;IACA;IACA3E,qBAAqB,CAACoE,gBAAuB,CAAC;IAC9C1C,iBAAiB,CAACc,KAAK,GAAG,IAAI;EAChC;EAEA,SAASsC,QAAQA,CAACzB,KAAwD,EAAE;IAC1E,SAAS;;IACT,IAAI,CAAC3B,iBAAiB,CAACc,KAAK,EAAE;MAC5B;IACF;IACA/B,eAAe,CAACS,aAAa,EAAEmC,KAAK,CAAC;IACrC,MAAMC,QAAQ,GAAGF,eAAe,CAACC,KAAK,CAAC;IACvC7B,WAAW,CAACgB,KAAK,GAAGa,KAAK;IACzB,MAAMzB,QAAQ,GAAGD,sBAAsB,CAACa,KAAK,CAACZ,QAAQ;IACtDtB,oBAAoB,CAACyE,gBAAgB,CAACnD,QAAQ,EAAE0B,QAAQ,CAAC;EAC3D;EAEA,SAAS0B,KAAKA,CAAC3B,KAAwD,EAAE;IACvE,SAAS;;IACT,IAAI,CAAC3B,iBAAiB,CAACc,KAAK,EAAE;MAC5B;IACF;IAEA,MAAMyC,cAAc,GAAG,GAAG;IAC1B,MAAMN,UAAU,GAAGhD,sBAAsB,CAACa,KAAK,CAACP,gBAAgB;IAChE,MAAMiD,SAAS,GACb7B,KAAK,CAACG,YAAY,GAAGM,IAAI,CAACqB,GAAG,CAAC9B,KAAK,CAAC+B,SAAS,GAAGH,cAAc,EAAE,GAAG,CAAC;IACtE,MAAMI,SAAS,GACbhC,KAAK,CAACM,YAAY,GAAGG,IAAI,CAACqB,GAAG,CAAC9B,KAAK,CAACiC,SAAS,GAAGL,cAAc,EAAE,GAAG,CAAC;IACtE,MAAMM,iBAAiB,GAAGZ,UAAU,CAAClB,KAAK,GAAG,CAAC;IAC9C,MAAM+B,iBAAiB,GAAGb,UAAU,CAACf,MAAM,GAAG,CAAC;IAC/C,MAAM5B,oBAAoB,GAAGtB,0BAA0B,CACrDQ,aAAa,EACbgE,SAAS,EACTK,iBAAiB,EACjBF,SAAS,EACTG,iBACF,CAAC;IACD,MAAM5D,QAAQ,GAAGD,sBAAsB,CAACa,KAAK,CAACZ,QAAQ;IACtDD,sBAAsB,CAACa,KAAK,CAACN,iBAAiB,GAAG,MAAM;MACrD5B,oBAAoB,CAACuE,gBAAgB,CAACjD,QAAQ,EAAEI,oBAAoB,CAAC;IACvE,CAAC;IACDL,sBAAsB,CAACa,KAAK,CAACR,oBAAoB,GAAGA,oBAAoB;IACxE;IACA;IACA;IACA;IACA;IACA;IACA/B,sBAAsB,CAAC0B,sBAAsB,CAACa,KAAY,CAAC;EAC7D;EAEA,IAAIiD,UAAU,GAAG5F,OAAO,CAAC6F,GAAG,CAAC,CAAC,CAC3BvB,OAAO,CAACA,OAAO,CAAC,CAChBW,QAAQ,CAACA,QAAQ,CAAC,CAClBE,KAAK,CAACA,KAAK,CAAC;EAEf,IAAI7D,iBAAiB,EAAE;IACrB,MAAMwE,aAAa,GAAG,EAAE;IACxB,MAAMC,mBAAmB,GAAG,EAAE;IAC9B,IAAI1E,aAAa,KAAK,YAAY,EAAE;MAClCuE,UAAU,GAAGA,UAAU,CACpBI,aAAa,CAACD,mBAAmB,CAAC,CAClCE,OAAO,CAAC;QAAEC,IAAI,EAAE,CAAC;QAAEC,GAAG,EAAE,CAAC;QAAEvC,KAAK,EAAEkC;MAAc,CAAC,CAAC;IACvD,CAAC,MAAM,IAAIzE,aAAa,KAAK,WAAW,EAAE;MACxCuE,UAAU,GAAGA,UAAU,CACpBI,aAAa,CAAC,CAACD,mBAAmB,CAAC,CACnCE,OAAO,CAAC;QAAEG,KAAK,EAAE,CAAC;QAAED,GAAG,EAAE,CAAC;QAAEvC,KAAK,EAAEkC;MAAc,CAAC,CAAC;IACxD,CAAC,MAAM,IAAIzE,aAAa,KAAK,WAAW,EAAE;MACxCuE,UAAU,GAAGA,UAAU,CACpBS,aAAa,CAACN,mBAAmB,CAAC,CAClCE,OAAO,CAAC;QAAEE,GAAG,EAAE,CAAC;QAAEpC,MAAM,EAAEnE,UAAU,CAAC0G,GAAG,CAAC,QAAQ,CAAC,CAACvC,MAAM,GAAG;MAAI,CAAC,CAAC;MACrE;IACF,CAAC,MAAM,IAAI1C,aAAa,KAAK,SAAS,EAAE;MACtCuE,UAAU,GAAGA,UAAU,CACpBS,aAAa,CAAC,CAACN,mBAAmB,CAAC,CACnCE,OAAO,CAAC;QAAEM,MAAM,EAAE,CAAC;QAAExC,MAAM,EAAE+B;MAAc,CAAC,CAAC;IAClD;EACF;EACA,oBACEpG,KAAA,CAAA8G,aAAA,CAACzG,eAAe;IAAC0G,OAAO,EAAEpF,aAAa,GAAGuE,UAAU,GAAG7E;EAAoB,GACxEI,QACc,CAAC;AAEtB,CAAC;AAED,eAAeD,qBAAqB", "ignoreList": []}