{"version": 3, "names": ["createHandler", "baseGestureHandlerProps", "flingGestureHandlerProps", "flingHandlerName", "FlingGestureHandler", "name", "allowedProps", "config"], "sourceRoot": "../../../src", "sources": ["handlers/FlingGestureHandler.ts"], "mappings": ";;AACA,OAAOA,aAAa,MAAM,iBAAiB;AAC3C,SAEEC,uBAAuB,QAClB,wBAAwB;AAE/B,OAAO,MAAMC,wBAAwB,GAAG,CACtC,kBAAkB,EAClB,WAAW,CACH;;AAyBV;AACA;AACA;;AAKA,OAAO,MAAMC,gBAAgB,GAAG,qBAAqB;;AAErD;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,GAAGJ,aAAa,CAG9C;EACAK,IAAI,EAAEF,gBAAgB;EACtBG,YAAY,EAAE,CACZ,GAAGL,uBAAuB,EAC1B,GAAGC,wBAAwB,CACnB;EACVK,MAAM,EAAE,CAAC;AACX,CAAC,CAAC", "ignoreList": []}