{"version": 3, "names": ["React", "jsx", "_jsx", "MULTIPLE_NAVIGATOR_ERROR", "SingleNavigatorContext", "createContext", "undefined", "EnsureSingleNavigator", "children", "navigator<PERSON><PERSON><PERSON><PERSON>", "useRef", "value", "useMemo", "register", "key", "current<PERSON><PERSON>", "current", "Error", "unregister", "Provider"], "sourceRoot": "../../src", "sources": ["EnsureSingleNavigator.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAM/B,MAAMC,wBAAwB,GAAG,oSAAoS;AAErU,OAAO,MAAMC,sBAAsB,gBAAGJ,KAAK,CAACK,aAAa,CAMvDC,SAAS,CAAC;;AAEZ;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAAC;EAAEC;AAAgB,CAAC,EAAE;EACzD,MAAMC,eAAe,GAAGT,KAAK,CAACU,MAAM,CAAqBJ,SAAS,CAAC;EAEnE,MAAMK,KAAK,GAAGX,KAAK,CAACY,OAAO,CACzB,OAAO;IACLC,QAAQA,CAACC,GAAW,EAAE;MACpB,MAAMC,UAAU,GAAGN,eAAe,CAACO,OAAO;MAE1C,IAAID,UAAU,KAAKT,SAAS,IAAIQ,GAAG,KAAKC,UAAU,EAAE;QAClD,MAAM,IAAIE,KAAK,CAACd,wBAAwB,CAAC;MAC3C;MAEAM,eAAe,CAACO,OAAO,GAAGF,GAAG;IAC/B,CAAC;IACDI,UAAUA,CAACJ,GAAW,EAAE;MACtB,MAAMC,UAAU,GAAGN,eAAe,CAACO,OAAO;MAE1C,IAAIF,GAAG,KAAKC,UAAU,EAAE;QACtB;MACF;MAEAN,eAAe,CAACO,OAAO,GAAGV,SAAS;IACrC;EACF,CAAC,CAAC,EACF,EACF,CAAC;EAED,oBACEJ,IAAA,CAACE,sBAAsB,CAACe,QAAQ;IAACR,KAAK,EAAEA,KAAM;IAAAH,QAAA,EAC3CA;EAAQ,CACsB,CAAC;AAEtC", "ignoreList": []}