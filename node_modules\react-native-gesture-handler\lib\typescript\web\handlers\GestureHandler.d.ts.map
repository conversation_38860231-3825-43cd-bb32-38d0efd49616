{"version": 3, "file": "GestureHandler.d.ts", "sourceRoot": "", "sources": ["../../../../src/web/handlers/GestureHandler.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AACpC,OAAO,EACL,MAAM,EACN,YAAY,EAOb,MAAM,eAAe,CAAC;AACvB,OAAO,YAAY,MAAM,uBAAuB,CAAC;AAGjD,OAAO,cAAkC,MAAM,yBAAyB,CAAC;AACzE,OAAO,eAAe,MAAM,mBAAmB,CAAC;AAChD,OAAO,EAAE,WAAW,EAAE,MAAM,qCAAqC,CAAC;AAClE,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAChD,OAAO,EAAE,sBAAsB,EAAE,MAAM,iCAAiC,CAAC;AAEzE,MAAM,CAAC,OAAO,CAAC,QAAQ,OAAO,cAAe,YAAW,eAAe;IACrE,OAAO,CAAC,aAAa,CAAsB;IAE3C,OAAO,CAAC,MAAM,CAA6B;IAE3C,OAAO,CAAC,wBAAwB,CAAS;IACzC,SAAS,CAAC,2BAA2B,UAAS;IAC9C,OAAO,CAAC,QAAQ,CAAS;IAEzB,OAAO,CAAC,OAAO,CAAU;IACzB,OAAO,CAAC,QAAQ,CAA4B;IAC5C,OAAO,CAAC,WAAW,CAAU;IAC7B,OAAO,CAAC,OAAO,CAA8B;IAE7C,OAAO,CAAC,QAAQ,CAAwC;IAGxD,OAAO,CAAC,gBAAgB,CAAK;IAE7B,OAAO,CAAC,SAAS,CAAS;IAC1B,OAAO,CAAC,OAAO,CAAS;IAExB,OAAO,CAAC,oBAAoB,CAAS;IACrC,OAAO,CAAC,YAAY,CAAkC;IAEtD,OAAO,CAAC,SAAS,CAAmD;gBAGlE,QAAQ,EAAE,sBAAsB,CAAC,OAAO,EAAE,eAAe,CAAC;IAS5D,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC;IAS3D,kBAAkB,CAAC,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,IAAI;IAqB/D,SAAS,CAAC,QAAQ,IAAI,IAAI;IAC1B,SAAS,CAAC,OAAO,IAAI,IAAI;IACzB,SAAS,CAAC,aAAa,IAAI,IAAI;IAExB,KAAK,IAAI,IAAI;IAYb,WAAW,CAAC,QAAQ,EAAE,KAAK,EAAE,cAAc,CAAC,EAAE,OAAO;IA8B5D,SAAS,CAAC,aAAa,CAAC,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,GAAG,IAAI;IAE1D,KAAK,IAAI,IAAI;IAUpB;;OAEG;IACI,IAAI,CAAC,cAAc,CAAC,EAAE,OAAO,GAAG,IAAI;IAY3C;;OAEG;IACI,MAAM,CAAC,cAAc,CAAC,EAAE,OAAO,GAAG,IAAI;IAetC,QAAQ,CAAC,KAAK,UAAQ;IAUtB,GAAG;IAeH,sBAAsB,IAAI,OAAO;IAGjC,sBAAsB,CAAC,KAAK,EAAE,OAAO,GAAG,IAAI;IAI5C,2BAA2B,CAAC,OAAO,EAAE,eAAe,GAAG,OAAO;IAW9D,6BAA6B,CAAC,OAAO,EAAE,eAAe,GAAG,OAAO;IAWhE,6BAA6B,CAAC,OAAO,EAAE,eAAe,GAAG,OAAO;IAWhE,wBAAwB,CAAC,OAAO,EAAE,eAAe,GAAG,OAAO;IAelE,SAAS,CAAC,aAAa,CAAC,KAAK,EAAE,YAAY,GAAG,IAAI;IAWlD,SAAS,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,GAAG,IAAI;IAGjD,SAAS,CAAC,WAAW,CAAC,KAAK,EAAE,YAAY,GAAG,IAAI;IAIhD,SAAS,CAAC,eAAe,CAAC,KAAK,EAAE,YAAY,GAAG,IAAI;IAGpD,SAAS,CAAC,aAAa,CAAC,KAAK,EAAE,YAAY,GAAG,IAAI;IAGlD,SAAS,CAAC,cAAc,CAAC,KAAK,EAAE,YAAY,GAAG,IAAI;IAenD,SAAS,CAAC,cAAc,CAAC,KAAK,EAAE,YAAY,GAAG,IAAI;IAGnD,SAAS,CAAC,eAAe,CAAC,KAAK,EAAE,YAAY,GAAG,IAAI;IAMpD,SAAS,CAAC,oBAAoB,CAAC,KAAK,EAAE,YAAY,GAAG,IAAI;IAGzD,SAAS,CAAC,iBAAiB,CAAC,MAAM,EAAE,YAAY,GAAG,IAAI;IAGvD,SAAS,CAAC,gBAAgB,CAAC,MAAM,EAAE,YAAY,GAAG,IAAI;IAGtD,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,YAAY,GAAG,IAAI;IAG7C,SAAS,CAAC,kBAAkB,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,YAAY,GAAG,IAAI;IAYrE,SAAS,CAAC,mBAAmB,CAAC,KAAK,EAAE,YAAY,GAAG,IAAI;IAMjD,cAAc,CAAC,KAAK,EAAE,YAAY,GAAG,IAAI;IAoBzC,SAAS,GAAI,UAAU,KAAK,EAAE,UAAU,KAAK,KAAG,IAAI,CAsBzD;IAEF,OAAO,CAAC,kBAAkB;IAkB1B,OAAO,CAAC,mBAAmB;IAmG3B,OAAO,CAAC,aAAa;IAmDrB,SAAS,CAAC,oBAAoB,IAAI,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;IAiBlD,mBAAmB,CAAC,EAAE,OAAc,EAAE,GAAG,KAAK,EAAE,EAAE,MAAM,GAAG,IAAI;IA6BtE,SAAS,CAAC,6BAA6B,CAAC,SAAS,EAAE,MAAM,EAAE,GAAG,IAAI;IAQlE,OAAO,CAAC,gBAAgB;IA8CxB,OAAO,CAAC,YAAY;IA+Db,gBAAgB,CAAC,WAAW,EAAE,WAAW,GAAG,SAAS;IAQ5D,SAAS,CAAC,WAAW,IAAI,IAAI;IAEtB,SAAS,IAAI,IAAI;IAQxB,IAAW,UAAU,IAGQ,MAAM,CADlC;IACD,IAAW,UAAU,CAAC,KAAK,EAAE,MAAM,EAElC;IAED,IAAW,MAAM,IAAI,MAAM,CAE1B;IAED,IAAW,QAAQ,qDAElB;IAED,IAAW,OAAO,mBAEjB;IAED,IAAW,KAAK,IAAI,KAAK,CAExB;IACD,SAAS,KAAK,KAAK,CAAC,KAAK,EAAE,KAAK,EAE/B;IAED,IAAW,uBAAuB,YAEjC;IACD,SAAS,KAAK,uBAAuB,CAAC,KAAK,SAAA,EAE1C;IAED,IAAW,OAAO,YAEjB;IACD,SAAS,KAAK,OAAO,CAAC,KAAK,SAAA,EAE1B;IAED,IAAW,WAAW,IAAI,WAAW,CAEpC;IACD,SAAS,KAAK,WAAW,CAAC,KAAK,EAAE,WAAW,EAE3C;IAED,IAAW,MAAM,YAEhB;IACD,SAAS,KAAK,MAAM,CAAC,KAAK,SAAA,EAEzB;IAED,IAAW,QAAQ,YAElB;IACD,SAAS,KAAK,QAAQ,CAAC,KAAK,SAAA,EAE3B;IAED,IAAW,eAAe,WAEzB;IACD,SAAS,KAAK,eAAe,CAAC,KAAK,QAAA,EAElC;IAED,IAAW,mBAAmB,YAE7B;IACD,SAAS,KAAK,mBAAmB,CAAC,KAAK,SAAA,EAEtC;IAEM,oBAAoB,IAAI,MAAM,EAAE;IAIvC,OAAO,CAAC,UAAU;CAOnB"}