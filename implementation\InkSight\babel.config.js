module.exports = {
  presets: [
    [
      'module:@react-native/babel-preset',
      { useTransformReactJSXExperimental: true },
    ],
    '@babel/preset-typescript',
  ],
  plugins: [['@babel/plugin-transform-react-jsx', { runtime: 'automatic' }]],
  env: {
    test: {
      presets: [
        [
          'module:@react-native/babel-preset',
          { useTransformReactJSXExperimental: true },
        ],
        ['@babel/preset-env', { targets: { node: 'current' } }],
        '@babel/preset-typescript',
        ['@babel/preset-react', { runtime: 'automatic' }],
      ],
      plugins: [
        ['@babel/plugin-transform-react-jsx', { runtime: 'automatic' }],
      ],
    },
  },
};
