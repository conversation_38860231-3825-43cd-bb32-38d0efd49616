{"version": 3, "names": ["React", "DeprecatedNavigationInChildContext", "NavigationBuilderContext", "shouldPreventRemove", "useOnPreventRemove", "useOnAction", "router", "getState", "setState", "key", "actionListeners", "beforeRemoveListeners", "routerConfigOptions", "emitter", "onAction", "onActionParent", "onRouteFocus", "onRouteFocusParent", "addListener", "addListenerParent", "onDispatchAction", "useContext", "navigationInChildEnabled", "routerConfigOptionsRef", "useRef", "useEffect", "current", "useCallback", "action", "visitedNavigators", "Set", "state", "has", "add", "target", "result", "getStateForAction", "isPrevented", "routes", "undefined", "shouldFocus", "shouldActionChangeFocus", "type", "i", "length", "listener"], "sourceRoot": "../../src", "sources": ["useOnAction.tsx"], "mappings": ";;AAOA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,kCAAkC,QAAQ,yCAAsC;AACzF,SAGEC,wBAAwB,QACnB,+BAA4B;AAGnC,SAASC,mBAAmB,EAAEC,kBAAkB,QAAQ,yBAAsB;AAa9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAAC;EAC1BC,MAAM;EACNC,QAAQ;EACRC,QAAQ;EACRC,GAAG;EACHC,eAAe;EACfC,qBAAqB;EACrBC,mBAAmB;EACnBC;AACO,CAAC,EAAE;EACV,MAAM;IACJC,QAAQ,EAAEC,cAAc;IACxBC,YAAY,EAAEC,kBAAkB;IAChCC,WAAW,EAAEC,iBAAiB;IAC9BC;EACF,CAAC,GAAGpB,KAAK,CAACqB,UAAU,CAACnB,wBAAwB,CAAC;EAC9C,MAAMoB,wBAAwB,GAAGtB,KAAK,CAACqB,UAAU,CAC/CpB,kCACF,CAAC;EAED,MAAMsB,sBAAsB,GAC1BvB,KAAK,CAACwB,MAAM,CAAsBZ,mBAAmB,CAAC;EAExDZ,KAAK,CAACyB,SAAS,CAAC,MAAM;IACpBF,sBAAsB,CAACG,OAAO,GAAGd,mBAAmB;EACtD,CAAC,CAAC;EAEF,MAAME,QAAQ,GAAGd,KAAK,CAAC2B,WAAW,CAChC,CACEC,MAAwB,EACxBC,iBAA8B,GAAG,IAAIC,GAAG,CAAS,CAAC,KAC/C;IACH,MAAMC,KAAK,GAAGxB,QAAQ,CAAC,CAAC;;IAExB;IACA;IACA,IAAIsB,iBAAiB,CAACG,GAAG,CAACD,KAAK,CAACtB,GAAG,CAAC,EAAE;MACpC,OAAO,KAAK;IACd;IAEAoB,iBAAiB,CAACI,GAAG,CAACF,KAAK,CAACtB,GAAG,CAAC;IAEhC,IAAI,OAAOmB,MAAM,CAACM,MAAM,KAAK,QAAQ,IAAIN,MAAM,CAACM,MAAM,KAAKH,KAAK,CAACtB,GAAG,EAAE;MACpE,IAAI0B,MAAM,GAAG7B,MAAM,CAAC8B,iBAAiB,CACnCL,KAAK,EACLH,MAAM,EACNL,sBAAsB,CAACG,OACzB,CAAC;;MAED;MACA;MACAS,MAAM,GACJA,MAAM,KAAK,IAAI,IAAIP,MAAM,CAACM,MAAM,KAAKH,KAAK,CAACtB,GAAG,GAAGsB,KAAK,GAAGI,MAAM;MAEjE,IAAIA,MAAM,KAAK,IAAI,EAAE;QACnBf,gBAAgB,CAACQ,MAAM,EAAEG,KAAK,KAAKI,MAAM,CAAC;QAE1C,IAAIJ,KAAK,KAAKI,MAAM,EAAE;UACpB,MAAME,WAAW,GAAGlC,mBAAmB,CACrCU,OAAO,EACPF,qBAAqB,EACrBoB,KAAK,CAACO,MAAM,EACZH,MAAM,CAACG,MAAM,EACbV,MACF,CAAC;UAED,IAAIS,WAAW,EAAE;YACf,OAAO,IAAI;UACb;UAEA7B,QAAQ,CAAC2B,MAAM,CAAC;QAClB;QAEA,IAAIlB,kBAAkB,KAAKsB,SAAS,EAAE;UACpC;UACA;UACA,MAAMC,WAAW,GAAGlC,MAAM,CAACmC,uBAAuB,CAACb,MAAM,CAAC;UAE1D,IAAIY,WAAW,IAAI/B,GAAG,KAAK8B,SAAS,EAAE;YACpCtB,kBAAkB,CAACR,GAAG,CAAC;UACzB;QACF;QAEA,OAAO,IAAI;MACb;IACF;IAEA,IAAIM,cAAc,KAAKwB,SAAS,EAAE;MAChC;MACA,IAAIxB,cAAc,CAACa,MAAM,EAAEC,iBAAiB,CAAC,EAAE;QAC7C,OAAO,IAAI;MACb;IACF;IAEA,IACE,OAAOD,MAAM,CAACM,MAAM,KAAK,QAAQ;IACjC;IACAN,MAAM,CAACc,IAAI,KAAK,qBAAqB,IACrCpB,wBAAwB,EACxB;MACA;MACA;MACA,KAAK,IAAIqB,CAAC,GAAGjC,eAAe,CAACkC,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACpD,MAAME,QAAQ,GAAGnC,eAAe,CAACiC,CAAC,CAAC;QAEnC,IAAIE,QAAQ,CAACjB,MAAM,EAAEC,iBAAiB,CAAC,EAAE;UACvC,OAAO,IAAI;QACb;MACF;IACF;IAEA,OAAO,KAAK;EACd,CAAC,EACD,CACEnB,eAAe,EACfC,qBAAqB,EACrBE,OAAO,EACPN,QAAQ,EACRe,wBAAwB,EACxBb,GAAG,EACHM,cAAc,EACdK,gBAAgB,EAChBH,kBAAkB,EAClBX,MAAM,EACNE,QAAQ,CAEZ,CAAC;EAEDJ,kBAAkB,CAAC;IACjBG,QAAQ;IACRM,OAAO;IACPF;EACF,CAAC,CAAC;EAEFX,KAAK,CAACyB,SAAS,CACb,MAAMN,iBAAiB,GAAG,QAAQ,EAAEL,QAAQ,CAAC,EAC7C,CAACK,iBAAiB,EAAEL,QAAQ,CAC9B,CAAC;EAED,OAAOA,QAAQ;AACjB", "ignoreList": []}