{"version": 3, "file": "ScreenNativeComponent.d.ts", "sourceRoot": "", "sources": ["../../../src/fabric/ScreenNativeComponent.ts"], "names": [], "mappings": ";AAGA,OAAO,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1D,OAAO,KAAK,EACV,kBAAkB,EAClB,WAAW,EACX,KAAK,EACL,KAAK,EACL,MAAM,EACP,MAAM,2CAA2C,CAAC;AAGnD,KAAK,WAAW,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC;AAEhC,KAAK,oBAAoB,GAAG,QAAQ,CAAC;IACnC,YAAY,EAAE,KAAK,CAAC;CACrB,CAAC,CAAC;AAEH,KAAK,uBAAuB,GAAG,QAAQ,CAAC;IACtC,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,KAAK,CAAC;IACf,YAAY,EAAE,KAAK,CAAC;CACrB,CAAC,CAAC;AAEH,KAAK,uBAAuB,GAAG,QAAQ,CAAC;IACtC,YAAY,EAAE,MAAM,CAAC;CACtB,CAAC,CAAC;AAEH,KAAK,uBAAuB,GAAG,QAAQ,CAAC;IACtC,KAAK,EAAE,KAAK,CAAC;IACb,QAAQ,EAAE,OAAO,CAAC;CACnB,CAAC,CAAC;AAEH,KAAK,2BAA2B,GAAG,QAAQ,CAAC;IAC1C,KAAK,EAAE,KAAK,CAAC;IACb,GAAG,EAAE,KAAK,CAAC;IACX,GAAG,EAAE,KAAK,CAAC;IACX,MAAM,EAAE,KAAK,CAAC;CACf,CAAC,CAAC;AAEH,KAAK,iBAAiB,GAClB,MAAM,GACN,OAAO,GACP,kBAAkB,GAClB,iBAAiB,GACjB,WAAW,GACX,WAAW,GACX,gBAAgB,GAChB,2BAA2B,CAAC;AAEhC,KAAK,cAAc,GACf,SAAS,GACT,MAAM,GACN,aAAa,GACb,MAAM,GACN,MAAM,GACN,kBAAkB,GAClB,iBAAiB,GACjB,mBAAmB,GACnB,kBAAkB,GAClB,gBAAgB,GAChB,eAAe,CAAC;AAEpB,KAAK,cAAc,GAAG,UAAU,GAAG,YAAY,CAAC;AAEhD,KAAK,gBAAgB,GAAG,KAAK,GAAG,MAAM,CAAC;AAEvC,MAAM,WAAW,WAAY,SAAQ,SAAS;IAC5C,QAAQ,CAAC,EAAE,kBAAkB,CAAC,WAAW,CAAC,CAAC;IAC3C,WAAW,CAAC,EAAE,kBAAkB,CAAC,WAAW,CAAC,CAAC;IAC9C,WAAW,CAAC,EAAE,kBAAkB,CAAC,oBAAoB,CAAC,CAAC;IACvD,wBAAwB,CAAC,EAAE,kBAAkB,CAAC,oBAAoB,CAAC,CAAC;IACpE,YAAY,CAAC,EAAE,kBAAkB,CAAC,WAAW,CAAC,CAAC;IAC/C,eAAe,CAAC,EAAE,kBAAkB,CAAC,WAAW,CAAC,CAAC;IAClD,oBAAoB,CAAC,EAAE,kBAAkB,CAAC,uBAAuB,CAAC,CAAC;IACnE,oBAAoB,CAAC,EAAE,kBAAkB,CAAC,uBAAuB,CAAC,CAAC;IACnE,eAAe,CAAC,EAAE,kBAAkB,CAAC,WAAW,CAAC,CAAC;IAClD,yBAAyB,CAAC,EAAE,kBAAkB,CAAC,WAAW,CAAC,CAAC;IAC5D,oBAAoB,CAAC,EAAE,kBAAkB,CAAC,uBAAuB,CAAC,CAAC;IACnE,mBAAmB,CAAC,EAAE,MAAM,EAAE,CAAC;IAC/B,0BAA0B,CAAC,EAAE,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;IACpD,mBAAmB,CAAC,EAAE,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAClD,iBAAiB,CAAC,EAAE,WAAW,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IAC7C,8BAA8B,CAAC,EAAE,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC7D,kBAAkB,CAAC,EAAE,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IAC3C,cAAc,CAAC,EAAE,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IACxC,sBAAsB,CAAC,EAAE,OAAO,CAAC;IACjC,sBAAsB,CAAC,EAAE,OAAO,CAAC;IACjC,4BAA4B,CAAC,EAAE,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC1D,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAC9B,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAC/B,cAAc,CAAC,EAAE,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC5C,cAAc,CAAC,EAAE,UAAU,CAAC;IAC5B,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAC/B,uBAAuB,CAAC,EAAE,2BAA2B,CAAC;IACtD,iBAAiB,CAAC,EAAE,WAAW,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;IAC3D,cAAc,CAAC,EAAE,WAAW,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;IACxD,kBAAkB,CAAC,EAAE,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC7C,gBAAgB,CAAC,EAAE,WAAW,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;IACxD,cAAc,CAAC,EAAE,WAAW,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;IAC3D,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAC9B,aAAa,CAAC,EAAE,WAAW,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IACzC,kBAAkB,CAAC,EAAE,UAAU,CAAC;IAChC,wBAAwB,CAAC,EAAE,OAAO,CAAC;IACnC,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAC9B,gCAAgC,CAAC,EAAE,OAAO,CAAC;CAC5C;;AAED,wBAEG"}