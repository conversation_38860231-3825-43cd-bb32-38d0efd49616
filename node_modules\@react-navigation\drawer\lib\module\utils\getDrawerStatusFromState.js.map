{"version": 3, "names": ["getDrawerStatusFromState", "state", "history", "Error", "entry", "findLast", "it", "type", "status", "default"], "sourceRoot": "../../../src", "sources": ["utils/getDrawerStatusFromState.tsx"], "mappings": ";;AAMA,OAAO,SAASA,wBAAwBA,CACtCC,KAA2C,EAC7B;EACd,IAAIA,KAAK,CAACC,OAAO,IAAI,IAAI,EAAE;IACzB,MAAM,IAAIC,KAAK,CACb,sGACF,CAAC;EACH;EAEA,MAAMC,KAAK,GAAGH,KAAK,CAACC,OAAO,CAACG,QAAQ,CAAEC,EAAE,IAAKA,EAAE,CAACC,IAAI,KAAK,QAAQ,CAEpD;EAEb,OAAOH,KAAK,EAAEI,MAAM,IAAIP,KAAK,CAACQ,OAAO,IAAI,QAAQ;AACnD", "ignoreList": []}