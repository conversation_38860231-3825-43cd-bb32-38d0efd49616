/**
 * Recent Screen
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { MainTabScreenProps } from '../navigation/types';

type Props = MainTabScreenProps<'Recent'>;

const RecentScreen: React.FC<Props> = ({ navigation }) => {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Recent Activity</Text>
        <Text style={styles.subtitle}>
          Your recently accessed documents and notes
        </Text>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recently Opened</Text>
          <Text style={styles.placeholder}>
            No recent activity. Start reading or taking notes to see your recent items here.
          </Text>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <Text style={styles.feature}>
            • Continue reading where you left off
          </Text>
          <Text style={styles.feature}>
            • Resume editing your last note
          </Text>
          <Text style={styles.feature}>
            • Access frequently used documents
          </Text>
          <Text style={styles.feature}>
            • View reading progress
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#757575',
    marginBottom: 32,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#424242',
    marginBottom: 16,
  },
  placeholder: {
    fontSize: 16,
    color: '#9E9E9E',
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 20,
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
  },
  feature: {
    fontSize: 16,
    color: '#424242',
    marginBottom: 8,
    paddingLeft: 16,
  },
});

export default RecentScreen;
