/**
 * Navigation Type Definitions
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import { StackNavigationProp } from '@react-navigation/stack';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import { DrawerNavigationProp as RNDrawerNavigationProp } from '@react-navigation/drawer';
import { RouteProp } from '@react-navigation/native';

// Root Stack Navigator Parameters
export type RootStackParamList = {
  // Authentication and onboarding
  Welcome: undefined;
  Login: undefined;
  Register: undefined;
  Onboarding: undefined;
  
  // Main application
  MainTabs: undefined;
  
  // Document-related screens
  DocumentViewer: {
    documentId: string;
    documentTitle?: string;
  };
  DocumentEditor: {
    documentId: string;
    mode: 'edit' | 'annotate';
  };
  
  // Note-related screens
  NoteEditor: {
    noteId?: string;
    documentId?: string;
  };
  NoteViewer: {
    noteId: string;
  };
  
  // Settings and configuration
  Settings: undefined;
  SettingsTheme: undefined;
  SettingsReading: undefined;
  SettingsHandwriting: undefined;
  SettingsPrivacy: undefined;
  SettingsAbout: undefined;
  
  // Handwriting recognition
  HandwritingCapture: {
    noteId?: string;
    documentId?: string;
  };
  HandwritingTraining: undefined;
  
  // Search and discovery
  Search: {
    query?: string;
    type?: 'documents' | 'notes' | 'all';
  };
  
  // Import and export
  ImportDocument: undefined;
  ExportOptions: {
    documentId?: string;
    noteId?: string;
  };
};

// Main Tab Navigator Parameters
export type MainTabParamList = {
  Library: undefined;
  Notes: undefined;
  Recent: undefined;
  Settings: undefined;
};

// Library Stack Navigator Parameters
export type LibraryStackParamList = {
  LibraryHome: undefined;
  DocumentList: {
    category?: string;
    sortBy?: 'name' | 'date' | 'size';
  };
  DocumentDetails: {
    documentId: string;
  };
};

// Notes Stack Navigator Parameters
export type NotesStackParamList = {
  NotesHome: undefined;
  NotesList: {
    category?: string;
    sortBy?: 'name' | 'date' | 'modified';
  };
  NoteDetails: {
    noteId: string;
  };
};

// Drawer Navigator Parameters
export type DrawerParamList = {
  Home: undefined;
  Library: undefined;
  Notes: undefined;
  Settings: undefined;
  Help: undefined;
  About: undefined;
};

// Navigation prop types for screens
export type RootStackNavigationProp<T extends keyof RootStackParamList> = 
  StackNavigationProp<RootStackParamList, T>;

export type MainTabNavigationProp<T extends keyof MainTabParamList> = 
  BottomTabNavigationProp<MainTabParamList, T>;

export type LibraryStackNavigationProp<T extends keyof LibraryStackParamList> = 
  StackNavigationProp<LibraryStackParamList, T>;

export type NotesStackNavigationProp<T extends keyof NotesStackParamList> = 
  StackNavigationProp<NotesStackParamList, T>;

export type DrawerNavigationProp<T extends keyof DrawerParamList> =
  RNDrawerNavigationProp<DrawerParamList, T>;

// Route prop types for screens
export type RootStackRouteProp<T extends keyof RootStackParamList> = 
  RouteProp<RootStackParamList, T>;

export type MainTabRouteProp<T extends keyof MainTabParamList> = 
  RouteProp<MainTabParamList, T>;

export type LibraryStackRouteProp<T extends keyof LibraryStackParamList> = 
  RouteProp<LibraryStackParamList, T>;

export type NotesStackRouteProp<T extends keyof NotesStackParamList> = 
  RouteProp<NotesStackParamList, T>;

// Combined navigation and route props for convenience
export type ScreenProps<
  NavigationProp,
  RouteProp
> = {
  navigation: NavigationProp;
  route: RouteProp;
};

// Common screen props
export type RootStackScreenProps<T extends keyof RootStackParamList> = 
  ScreenProps<RootStackNavigationProp<T>, RootStackRouteProp<T>>;

export type MainTabScreenProps<T extends keyof MainTabParamList> = 
  ScreenProps<MainTabNavigationProp<T>, MainTabRouteProp<T>>;

export type LibraryStackScreenProps<T extends keyof LibraryStackParamList> = 
  ScreenProps<LibraryStackNavigationProp<T>, LibraryStackRouteProp<T>>;

export type NotesStackScreenProps<T extends keyof NotesStackParamList> = 
  ScreenProps<NotesStackNavigationProp<T>, NotesStackRouteProp<T>>;

// Deep linking configuration
export type LinkingConfig = {
  prefixes: string[];
  config: {
    screens: {
      [K in keyof RootStackParamList]: string;
    };
  };
};

// Navigation state types
export interface NavigationState {
  currentRoute: string;
  previousRoute?: string;
  params?: Record<string, any>;
  timestamp: number;
}

// Navigation analytics types
export interface NavigationEvent {
  screen: string;
  action: 'navigate' | 'goBack' | 'reset';
  timestamp: number;
  params?: Record<string, any>;
}

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}
