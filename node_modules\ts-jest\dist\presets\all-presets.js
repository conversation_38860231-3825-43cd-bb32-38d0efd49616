"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const create_jest_preset_1 = require("./create-jest-preset");
const allPresets = {
    get defaults() {
        return (0, create_jest_preset_1.createDefaultPreset)();
    },
    get defaultsLegacy() {
        return (0, create_jest_preset_1.createDefaultLegacyPreset)();
    },
    get defaultsESM() {
        return (0, create_jest_preset_1.createDefaultEsmPreset)();
    },
    get defaultsESMLegacy() {
        return (0, create_jest_preset_1.createDefaultEsmLegacyPreset)();
    },
    get jsWithTs() {
        return (0, create_jest_preset_1.createJsWithTsPreset)();
    },
    get jsWithTsLegacy() {
        return (0, create_jest_preset_1.createJsWithTsLegacyPreset)();
    },
    get jsWithTsESM() {
        return (0, create_jest_preset_1.createJsWithTsEsmPreset)();
    },
    get jsWithTsESMLegacy() {
        return (0, create_jest_preset_1.createJsWithTsEsmLegacyPreset)();
    },
    get jsWithBabel() {
        return (0, create_jest_preset_1.createJsWithBabelPreset)();
    },
    get jsWithBabelLegacy() {
        return (0, create_jest_preset_1.createJsWithBabelLegacyPreset)();
    },
    get jsWithBabelESM() {
        return (0, create_jest_preset_1.createJsWithBabelEsmPreset)();
    },
    get jsWithBabelESMLegacy() {
        return (0, create_jest_preset_1.createJsWithBabelEsmLegacyPreset)();
    },
};
exports.default = allPresets;
