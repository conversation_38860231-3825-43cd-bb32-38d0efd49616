{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_createNativeWrapper", "_interopRequireDefault", "_GestureHandlerButton", "_State", "_utils", "_jsxRuntime", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "RawButton", "exports", "createNativeWrapper", "GestureHandlerButton", "shouldCancelWhenOutside", "shouldActivateOnStart", "IS_FABRIC", "InnerBaseButton", "Component", "defaultProps", "delayLongPress", "constructor", "props", "lastActive", "longPressDetected", "handleEvent", "nativeEvent", "state", "oldState", "pointerInside", "active", "State", "ACTIVE", "onActiveStateChange", "CANCELLED", "onPress", "Platform", "OS", "BEGAN", "onLongPress", "longPressTimeout", "setTimeout", "undefined", "clearTimeout", "END", "FAILED", "onHandlerStateChange", "onGestureEvent", "render", "rippleColor", "unprocessedRippleColor", "style", "rest", "isF<PERSON><PERSON>", "processColor", "jsx", "ref", "innerRef", "cursor", "AnimatedInnerBaseButton", "Animated", "createAnimatedComponent", "BaseButton", "forwardRef", "AnimatedBaseButton", "btnStyles", "StyleSheet", "create", "underlay", "position", "left", "right", "bottom", "top", "InnerRectButton", "activeOpacity", "underlayColor", "opacity", "Value", "setValue", "children", "resolvedStyle", "flatten", "jsxs", "View", "backgroundColor", "borderRadius", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "RectButton", "InnerBorderlessButton", "borderless", "BorderlessButton"], "sourceRoot": "../../../src", "sources": ["components/GestureButtons.tsx"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,oBAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,qBAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AAeA,IAAAM,MAAA,GAAAN,OAAA;AAAoC,IAAAO,WAAA,GAAAP,OAAA;AAAA,SAAAG,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAT,wBAAAS,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAb,uBAAA,YAAAA,CAAAS,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAE7B,MAAMgB,SAAS,GAAAC,OAAA,CAAAD,SAAA,GAAG,IAAAE,4BAAmB,EAACC,6BAAoB,EAAE;EACjEC,uBAAuB,EAAE,KAAK;EAC9BC,qBAAqB,EAAE;AACzB,CAAC,CAAC;AAEF,IAAIC,SAAyB,GAAG,IAAI;AAEpC,MAAMC,eAAe,SAASpC,KAAK,CAACqC,SAAS,CAAyB;EACpE,OAAOC,YAAY,GAAG;IACpBC,cAAc,EAAE;EAClB,CAAC;EAMDC,WAAWA,CAACC,KAA6B,EAAE;IACzC,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,iBAAiB,GAAG,KAAK;EAChC;EAEQC,WAAW,GAAGA,CAAC;IACrBC;EACwD,CAAC,KAAK;IAC9D,MAAM;MAAEC,KAAK;MAAEC,QAAQ;MAAEC;IAAc,CAAC,GAAGH,WAAW;IACtD,MAAMI,MAAM,GAAGD,aAAa,IAAIF,KAAK,KAAKI,YAAK,CAACC,MAAM;IAEtD,IAAIF,MAAM,KAAK,IAAI,CAACP,UAAU,IAAI,IAAI,CAACD,KAAK,CAACW,mBAAmB,EAAE;MAChE,IAAI,CAACX,KAAK,CAACW,mBAAmB,CAACH,MAAM,CAAC;IACxC;IAEA,IACE,CAAC,IAAI,CAACN,iBAAiB,IACvBI,QAAQ,KAAKG,YAAK,CAACC,MAAM,IACzBL,KAAK,KAAKI,YAAK,CAACG,SAAS,IACzB,IAAI,CAACX,UAAU,IACf,IAAI,CAACD,KAAK,CAACa,OAAO,EAClB;MACA,IAAI,CAACb,KAAK,CAACa,OAAO,CAACN,aAAa,CAAC;IACnC;IAEA,IACE,CAAC,IAAI,CAACN,UAAU;IAChB;IACAI,KAAK,MAAMS,qBAAQ,CAACC,EAAE,KAAK,SAAS,GAAGN,YAAK,CAACC,MAAM,GAAGD,YAAK,CAACO,KAAK,CAAC,IAClET,aAAa,EACb;MACA,IAAI,CAACL,iBAAiB,GAAG,KAAK;MAC9B,IAAI,IAAI,CAACF,KAAK,CAACiB,WAAW,EAAE;QAC1B,IAAI,CAACC,gBAAgB,GAAGC,UAAU,CAChC,IAAI,CAACF,WAAW,EAChB,IAAI,CAACjB,KAAK,CAACF,cACb,CAAC;MACH;IACF,CAAC,MAAM;IACL;IACAO,KAAK,KAAKI,YAAK,CAACC,MAAM,IACtB,CAACH,aAAa,IACd,IAAI,CAACW,gBAAgB,KAAKE,SAAS,EACnC;MACAC,YAAY,CAAC,IAAI,CAACH,gBAAgB,CAAC;MACnC,IAAI,CAACA,gBAAgB,GAAGE,SAAS;IACnC,CAAC,MAAM;IACL;IACA,IAAI,CAACF,gBAAgB,KAAKE,SAAS,KAClCf,KAAK,KAAKI,YAAK,CAACa,GAAG,IAClBjB,KAAK,KAAKI,YAAK,CAACG,SAAS,IACzBP,KAAK,KAAKI,YAAK,CAACc,MAAM,CAAC,EACzB;MACAF,YAAY,CAAC,IAAI,CAACH,gBAAgB,CAAC;MACnC,IAAI,CAACA,gBAAgB,GAAGE,SAAS;IACnC;IAEA,IAAI,CAACnB,UAAU,GAAGO,MAAM;EAC1B,CAAC;EAEOS,WAAW,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACf,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACF,KAAK,CAACiB,WAAW,GAAG,CAAC;EAC5B,CAAC;;EAED;EACA;EACA;EACA;EACQO,oBAAoB,GAC1BvD,CAA2D,IACxD;IACH,IAAI,CAAC+B,KAAK,CAACwB,oBAAoB,GAAGvD,CAAC,CAAC;IACpC,IAAI,CAACkC,WAAW,CAAClC,CAAC,CAAC;EACrB,CAAC;EAEOwD,cAAc,GACpBxD,CAAgD,IAC7C;IACH,IAAI,CAAC+B,KAAK,CAACyB,cAAc,GAAGxD,CAAC,CAAC;IAC9B,IAAI,CAACkC,WAAW,CACdlC,CACF,CAAC,CAAC,CAAC;EACL,CAAC;EAEDyD,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEC,WAAW,EAAEC,sBAAsB;MAAEC,KAAK;MAAE,GAAGC;IAAK,CAAC,GAAG,IAAI,CAAC9B,KAAK;IAE1E,IAAIN,SAAS,KAAK,IAAI,EAAE;MACtBA,SAAS,GAAG,IAAAqC,eAAQ,EAAC,CAAC;IACxB;IAEA,MAAMJ,WAAW,GAAGjC,SAAS,GACzBkC,sBAAsB,GACtB,IAAAI,yBAAY,EAACJ,sBAAsB,IAAIR,SAAS,CAAC;IAErD,oBACE,IAAApD,WAAA,CAAAiE,GAAA,EAAC7C,SAAS;MACR8C,GAAG,EAAE,IAAI,CAAClC,KAAK,CAACmC,QAAS;MACzBR,WAAW,EAAEA,WAAY;MACzBE,KAAK,EAAE,CAACA,KAAK,EAAEf,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAI;QAAEqB,MAAM,EAAEhB;MAAU,CAAC,CAAE;MAAA,GAC3DU,IAAI;MACRL,cAAc,EAAE,IAAI,CAACA,cAAe;MACpCD,oBAAoB,EAAE,IAAI,CAACA;IAAqB,CACjD,CAAC;EAEN;AACF;AAEA,MAAMa,uBAAuB,GAC3BC,qBAAQ,CAACC,uBAAuB,CAAyB5C,eAAe,CAAC;AAEpE,MAAM6C,UAAU,GAAAnD,OAAA,CAAAmD,UAAA,gBAAGjF,KAAK,CAACkF,UAAU,CAGxC,CAACzC,KAAK,EAAEkC,GAAG,kBAAK,IAAAlE,WAAA,CAAAiE,GAAA,EAACtC,eAAe;EAACwC,QAAQ,EAAED,GAAI;EAAA,GAAKlC;AAAK,CAAG,CAAC,CAAC;AAEhE,MAAM0C,kBAAkB,gBAAGnF,KAAK,CAACkF,UAAU,CAGzC,CAACzC,KAAK,EAAEkC,GAAG,kBAAK,IAAAlE,WAAA,CAAAiE,GAAA,EAACI,uBAAuB;EAACF,QAAQ,EAAED,GAAI;EAAA,GAAKlC;AAAK,CAAG,CAAC,CAAC;AAExE,MAAM2C,SAAS,GAAGC,uBAAU,CAACC,MAAM,CAAC;EAClCC,QAAQ,EAAE;IACRC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,GAAG,EAAE;EACP;AACF,CAAC,CAAC;AAEF,MAAMC,eAAe,SAAS7F,KAAK,CAACqC,SAAS,CAAyB;EACpE,OAAOC,YAAY,GAAG;IACpBwD,aAAa,EAAE,KAAK;IACpBC,aAAa,EAAE;EACjB,CAAC;EAIDvD,WAAWA,CAACC,KAA6B,EAAE;IACzC,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACuD,OAAO,GAAG,IAAIjB,qBAAQ,CAACkB,KAAK,CAAC,CAAC,CAAC;EACtC;EAEQ7C,mBAAmB,GAAIH,MAAe,IAAK;IACjD,IAAIM,qBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;MAC7B,IAAI,CAACwC,OAAO,CAACE,QAAQ,CAACjD,MAAM,GAAG,IAAI,CAACR,KAAK,CAACqD,aAAa,GAAI,CAAC,CAAC;IAC/D;IAEA,IAAI,CAACrD,KAAK,CAACW,mBAAmB,GAAGH,MAAM,CAAC;EAC1C,CAAC;EAEDkB,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEgC,QAAQ;MAAE7B,KAAK;MAAE,GAAGC;IAAK,CAAC,GAAG,IAAI,CAAC9B,KAAK;IAE/C,MAAM2D,aAAa,GAAGf,uBAAU,CAACgB,OAAO,CAAC/B,KAAK,CAAC,IAAI,CAAC,CAAC;IAErD,oBACE,IAAA7D,WAAA,CAAA6F,IAAA,EAACrB,UAAU;MAAA,GACLV,IAAI;MACRI,GAAG,EAAE,IAAI,CAAClC,KAAK,CAACmC,QAAS;MACzBN,KAAK,EAAE8B,aAAc;MACrBhD,mBAAmB,EAAE,IAAI,CAACA,mBAAoB;MAAA+C,QAAA,gBAC9C,IAAA1F,WAAA,CAAAiE,GAAA,EAACvE,YAAA,CAAA4E,QAAQ,CAACwB,IAAI;QACZjC,KAAK,EAAE,CACLc,SAAS,CAACG,QAAQ,EAClB;UACES,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBQ,eAAe,EAAE,IAAI,CAAC/D,KAAK,CAACsD,aAAa;UACzCU,YAAY,EAAEL,aAAa,CAACK,YAAY;UACxCC,mBAAmB,EAAEN,aAAa,CAACM,mBAAmB;UACtDC,oBAAoB,EAAEP,aAAa,CAACO,oBAAoB;UACxDC,sBAAsB,EAAER,aAAa,CAACQ,sBAAsB;UAC5DC,uBAAuB,EAAET,aAAa,CAACS;QACzC,CAAC;MACD,CACH,CAAC,EACDV,QAAQ;IAAA,CACC,CAAC;EAEjB;AACF;AAEO,MAAMW,UAAU,GAAAhF,OAAA,CAAAgF,UAAA,gBAAG9G,KAAK,CAACkF,UAAU,CAGxC,CAACzC,KAAK,EAAEkC,GAAG,kBAAK,IAAAlE,WAAA,CAAAiE,GAAA,EAACmB,eAAe;EAACjB,QAAQ,EAAED,GAAI;EAAA,GAAKlC;AAAK,CAAG,CAAC,CAAC;AAEhE,MAAMsE,qBAAqB,SAAS/G,KAAK,CAACqC,SAAS,CAA+B;EAChF,OAAOC,YAAY,GAAG;IACpBwD,aAAa,EAAE,GAAG;IAClBkB,UAAU,EAAE;EACd,CAAC;EAIDxE,WAAWA,CAACC,KAAmC,EAAE;IAC/C,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACuD,OAAO,GAAG,IAAIjB,qBAAQ,CAACkB,KAAK,CAAC,CAAC,CAAC;EACtC;EAEQ7C,mBAAmB,GAAIH,MAAe,IAAK;IACjD,IAAIM,qBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;MAC7B,IAAI,CAACwC,OAAO,CAACE,QAAQ,CAACjD,MAAM,GAAG,IAAI,CAACR,KAAK,CAACqD,aAAa,GAAI,CAAC,CAAC;IAC/D;IAEA,IAAI,CAACrD,KAAK,CAACW,mBAAmB,GAAGH,MAAM,CAAC;EAC1C,CAAC;EAEDkB,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEgC,QAAQ;MAAE7B,KAAK;MAAEM,QAAQ;MAAE,GAAGL;IAAK,CAAC,GAAG,IAAI,CAAC9B,KAAK;IAEzD,oBACE,IAAAhC,WAAA,CAAAiE,GAAA,EAACS,kBAAkB;MAAA,GACbZ,IAAI;MACRK,QAAQ,EAAEA,QAAS;MACnBxB,mBAAmB,EAAE,IAAI,CAACA,mBAAoB;MAC9CkB,KAAK,EAAE,CAACA,KAAK,EAAEf,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAI;QAAEwC,OAAO,EAAE,IAAI,CAACA;MAAQ,CAAC,CAAE;MAAAG,QAAA,EAClEA;IAAQ,CACS,CAAC;EAEzB;AACF;AAEO,MAAMc,gBAAgB,GAAAnF,OAAA,CAAAmF,gBAAA,gBAAGjH,KAAK,CAACkF,UAAU,CAG9C,CAACzC,KAAK,EAAEkC,GAAG,kBAAK,IAAAlE,WAAA,CAAAiE,GAAA,EAACqC,qBAAqB;EAACnC,QAAQ,EAAED,GAAI;EAAA,GAAKlC;AAAK,CAAG,CAAC,CAAC", "ignoreList": []}