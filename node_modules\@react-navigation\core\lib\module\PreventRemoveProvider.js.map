{"version": 3, "names": ["nanoid", "React", "useLatestCallback", "NavigationHelpersContext", "NavigationRouteContext", "PreventRemoveContext", "jsx", "_jsx", "transformPreventedRoutes", "preventedRoutesMap", "preventedRoutesToTransform", "values", "preventedRoutes", "reduce", "acc", "routeKey", "preventRemove", "PreventRemoveProvider", "children", "parentId", "useState", "setPreventedRoutesMap", "Map", "navigation", "useContext", "route", "preventRemoveContextValue", "setParentPrevented", "setPreventRemove", "id", "getState", "routes", "every", "key", "Error", "prevPrevented", "get", "nextPrevented", "set", "delete", "isPrevented", "some", "useEffect", "undefined", "value", "useMemo", "Provider"], "sourceRoot": "../../src", "sources": ["PreventRemoveProvider.tsx"], "mappings": ";;AAAA,SAASA,MAAM,QAAQ,mBAAmB;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,qBAAqB;AAEnD,SAASC,wBAAwB,QAAQ,+BAA4B;AACrE,SAASC,sBAAsB,QAAQ,6BAA0B;AACjE,SAEEC,oBAAoB,QACf,2BAAwB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAchC;AACA;AACA;AACA,MAAMC,wBAAwB,GAC5BC,kBAAsC,IAClB;EACpB,MAAMC,0BAA0B,GAAG,CAAC,GAAGD,kBAAkB,CAACE,MAAM,CAAC,CAAC,CAAC;EAEnE,MAAMC,eAAe,GAAGF,0BAA0B,CAACG,MAAM,CACvD,CAACC,GAAG,EAAE;IAAEC,QAAQ;IAAEC;EAAc,CAAC,KAAK;IACpCF,GAAG,CAACC,QAAQ,CAAC,GAAG;MACdC,aAAa,EAAEF,GAAG,CAACC,QAAQ,CAAC,EAAEC,aAAa,IAAIA;IACjD,CAAC;IACD,OAAOF,GAAG;EACZ,CAAC,EACD,CAAC,CACH,CAAC;EAED,OAAOF,eAAe;AACxB,CAAC;;AAED;AACA;AACA;AACA,OAAO,SAASK,qBAAqBA,CAAC;EAAEC;AAAgB,CAAC,EAAE;EACzD,MAAM,CAACC,QAAQ,CAAC,GAAGlB,KAAK,CAACmB,QAAQ,CAAC,MAAMpB,MAAM,CAAC,CAAC,CAAC;EACjD,MAAM,CAACS,kBAAkB,EAAEY,qBAAqB,CAAC,GAC/CpB,KAAK,CAACmB,QAAQ,CAAqB,MAAM,IAAIE,GAAG,CAAC,CAAC,CAAC;EAErD,MAAMC,UAAU,GAAGtB,KAAK,CAACuB,UAAU,CAACrB,wBAAwB,CAAC;EAC7D,MAAMsB,KAAK,GAAGxB,KAAK,CAACuB,UAAU,CAACpB,sBAAsB,CAAC;EAEtD,MAAMsB,yBAAyB,GAAGzB,KAAK,CAACuB,UAAU,CAACnB,oBAAoB,CAAC;EACxE;EACA,MAAMsB,kBAAkB,GAAGD,yBAAyB,EAAEE,gBAAgB;EAEtE,MAAMA,gBAAgB,GAAG1B,iBAAiB,CACxC,CAAC2B,EAAU,EAAEd,QAAgB,EAAEC,aAAsB,KAAW;IAC9D,IACEA,aAAa,KACZO,UAAU,IAAI,IAAI,IACjBA,UAAU,EACNO,QAAQ,CAAC,CAAC,CACXC,MAAM,CAACC,KAAK,CAAEP,KAAK,IAAKA,KAAK,CAACQ,GAAG,KAAKlB,QAAQ,CAAC,CAAC,EACrD;MACA,MAAM,IAAImB,KAAK,CACb,sCAAsCnB,QAAQ,+CAChD,CAAC;IACH;IAEAM,qBAAqB,CAAEc,aAAa,IAAK;MACvC;MACA,IACEpB,QAAQ,KAAKoB,aAAa,CAACC,GAAG,CAACP,EAAE,CAAC,EAAEd,QAAQ,IAC5CC,aAAa,KAAKmB,aAAa,CAACC,GAAG,CAACP,EAAE,CAAC,EAAEb,aAAa,EACtD;QACA,OAAOmB,aAAa;MACtB;MAEA,MAAME,aAAa,GAAG,IAAIf,GAAG,CAACa,aAAa,CAAC;MAE5C,IAAInB,aAAa,EAAE;QACjBqB,aAAa,CAACC,GAAG,CAACT,EAAE,EAAE;UACpBd,QAAQ;UACRC;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLqB,aAAa,CAACE,MAAM,CAACV,EAAE,CAAC;MAC1B;MAEA,OAAOQ,aAAa;IACtB,CAAC,CAAC;EACJ,CACF,CAAC;EAED,MAAMG,WAAW,GAAG,CAAC,GAAG/B,kBAAkB,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC8B,IAAI,CACvD,CAAC;IAAEzB;EAAc,CAAC,KAAKA,aACzB,CAAC;EAEDf,KAAK,CAACyC,SAAS,CAAC,MAAM;IACpB,IAAIjB,KAAK,EAAEQ,GAAG,KAAKU,SAAS,IAAIhB,kBAAkB,KAAKgB,SAAS,EAAE;MAChE;MACA;MACAhB,kBAAkB,CAACR,QAAQ,EAAEM,KAAK,CAACQ,GAAG,EAAEO,WAAW,CAAC;MACpD,OAAO,MAAM;QACXb,kBAAkB,CAACR,QAAQ,EAAEM,KAAK,CAACQ,GAAG,EAAE,KAAK,CAAC;MAChD,CAAC;IACH;IAEA;EACF,CAAC,EAAE,CAACd,QAAQ,EAAEqB,WAAW,EAAEf,KAAK,EAAEQ,GAAG,EAAEN,kBAAkB,CAAC,CAAC;EAE3D,MAAMiB,KAAK,GAAG3C,KAAK,CAAC4C,OAAO,CACzB,OAAO;IACLjB,gBAAgB;IAChBhB,eAAe,EAAEJ,wBAAwB,CAACC,kBAAkB;EAC9D,CAAC,CAAC,EACF,CAACmB,gBAAgB,EAAEnB,kBAAkB,CACvC,CAAC;EAED,oBACEF,IAAA,CAACF,oBAAoB,CAACyC,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAA1B,QAAA,EACzCA;EAAQ,CACoB,CAAC;AAEpC", "ignoreList": []}