"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _interfaces = require("../interfaces");
var _EventManager = _interopRequireDefault(require("./EventManager"));
var _PointerType = require("../../PointerType");
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
class KeyboardEventManager extends _EventManager.default {
  activationKeys = ['Enter', ' '];
  cancelationKeys = ['Tab'];
  isPressed = false;
  keyDownCallback = event => {
    if (this.cancelationKeys.indexOf(event.key) !== -1 && this.isPressed) {
      this.dispatchEvent(event, _interfaces.EventTypes.CANCEL);
      return;
    }
    if (this.activationKeys.indexOf(event.key) === -1) {
      return;
    }
    this.dispatchEvent(event, _interfaces.EventTypes.DOWN);
  };
  keyUpCallback = event => {
    if (this.activationKeys.indexOf(event.key) === -1 || !this.isPressed) {
      return;
    }
    this.dispatchEvent(event, _interfaces.EventTypes.UP);
  };
  dispatchEvent(event, eventType) {
    if (!(event.target instanceof HTMLElement)) {
      return;
    }
    const adaptedEvent = this.mapEvent(event, eventType);
    switch (eventType) {
      case _interfaces.EventTypes.UP:
        this.isPressed = false;
        this.onPointerUp(adaptedEvent);
        break;
      case _interfaces.EventTypes.DOWN:
        this.isPressed = true;
        this.onPointerDown(adaptedEvent);
        break;
      case _interfaces.EventTypes.CANCEL:
        this.isPressed = false;
        this.onPointerCancel(adaptedEvent);
        break;
    }
  }
  registerListeners() {
    this.view.addEventListener('keydown', this.keyDownCallback);
    this.view.addEventListener('keyup', this.keyUpCallback);
  }
  unregisterListeners() {
    this.view.removeEventListener('keydown', this.keyDownCallback);
    this.view.removeEventListener('keyup', this.keyUpCallback);
  }
  mapEvent(event, eventType) {
    const viewRect = event.target.getBoundingClientRect();
    const viewportPosition = {
      x: viewRect?.x + viewRect?.width / 2,
      y: viewRect?.y + viewRect?.height / 2
    };
    const relativePosition = {
      x: viewRect?.width / 2,
      y: viewRect?.height / 2
    };
    return {
      x: viewportPosition.x,
      y: viewportPosition.y,
      offsetX: relativePosition.x,
      offsetY: relativePosition.y,
      pointerId: 0,
      eventType: eventType,
      pointerType: _PointerType.PointerType.KEY,
      time: event.timeStamp
    };
  }
}
exports.default = KeyboardEventManager;
//# sourceMappingURL=KeyboardEventManager.js.map