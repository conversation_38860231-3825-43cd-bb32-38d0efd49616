{"version": 3, "names": ["queryString", "getPatternParts", "validatePathConfig", "getActiveRoute", "state", "route", "index", "routes", "length", "cachedNormalizedConfigs", "WeakMap", "getNormalizedConfigs", "options", "screens", "cached", "get", "normalizedConfigs", "createNormalizedConfigs", "set", "getPathFromState", "Error", "String", "configs", "path", "current", "allParams", "parts", "focusedParams", "currentOptions", "focusedRoute", "nestedRouteNames", "hasNext", "name", "push", "params", "currentParams", "Object", "fromEntries", "entries", "map", "key", "value", "undefined", "optional", "find", "part", "param", "stringify", "filter", "entry", "assign", "for<PERSON>ach", "nextRoute", "nestedConfig", "segment", "replace", "char", "encodeURIComponent", "join", "query", "sort", "startsWith", "createConfigItem", "config", "parentParts", "exact", "c", "result"], "sourceRoot": "../../src", "sources": ["getPathFromState.tsx"], "mappings": ";;AAKA,OAAO,KAAKA,WAAW,MAAM,cAAc;AAE3C,SAASC,eAAe,QAA0B,sBAAmB;AAErE,SAASC,kBAAkB,QAAQ,yBAAsB;AAkBzD,MAAMC,cAAc,GAAIC,KAAY,IAAwC;EAC1E,MAAMC,KAAK,GACT,OAAOD,KAAK,CAACE,KAAK,KAAK,QAAQ,GAC3BF,KAAK,CAACG,MAAM,CAACH,KAAK,CAACE,KAAK,CAAC,GACzBF,KAAK,CAACG,MAAM,CAACH,KAAK,CAACG,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;EAE3C,IAAIH,KAAK,CAACD,KAAK,EAAE;IACf,OAAOD,cAAc,CAACE,KAAK,CAACD,KAAK,CAAC;EACpC;EAEA,OAAOC,KAAK;AACd,CAAC;AAED,MAAMI,uBAAuB,GAAG,IAAIC,OAAO,CAGzC,CAAC;AAEH,MAAMC,oBAAoB,GAAIC,OAAqB,IAAK;EACtD,IAAI,CAACA,OAAO,EAAEC,OAAO,EAAE,OAAO,CAAC,CAAC;EAEhC,MAAMC,MAAM,GAAGL,uBAAuB,CAACM,GAAG,CAACH,OAAO,EAAEC,OAAO,CAAC;EAE5D,IAAIC,MAAM,EAAE,OAAOA,MAAM;EAEzB,MAAME,iBAAiB,GAAGC,uBAAuB,CAACL,OAAO,CAACC,OAAO,CAAC;EAElEJ,uBAAuB,CAACS,GAAG,CAACN,OAAO,CAACC,OAAO,EAAEG,iBAAiB,CAAC;EAE/D,OAAOA,iBAAiB;AAC1B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,gBAAgBA,CAC9Bf,KAAY,EACZQ,OAA4B,EACpB;EACR,IAAIR,KAAK,IAAI,IAAI,EAAE;IACjB,MAAMgB,KAAK,CACT,QAAQC,MAAM,CAACjB,KAAK,CAAC,iEACvB,CAAC;EACH;EAEA,IAAIQ,OAAO,EAAE;IACXV,kBAAkB,CAACU,OAAO,CAAC;EAC7B;EAEA,MAAMU,OAAO,GAAGX,oBAAoB,CAACC,OAAO,CAAC;EAE7C,IAAIW,IAAI,GAAG,GAAG;EACd,IAAIC,OAA0B,GAAGpB,KAAK;EAEtC,MAAMqB,SAAiC,GAAG,CAAC,CAAC;EAE5C,OAAOD,OAAO,EAAE;IACd,IAAIlB,KAAK,GAAG,OAAOkB,OAAO,CAAClB,KAAK,KAAK,QAAQ,GAAGkB,OAAO,CAAClB,KAAK,GAAG,CAAC;IACjE,IAAID,KAAK,GAAGmB,OAAO,CAACjB,MAAM,CAACD,KAAK,CAE/B;IAED,IAAIoB,KAAgC;IAEpC,IAAIC,aAAiD;IACrD,IAAIC,cAAc,GAAGN,OAAO;IAE5B,MAAMO,YAAY,GAAG1B,cAAc,CAACC,KAAK,CAAC;;IAE1C;IACA,MAAM0B,gBAAgB,GAAG,EAAE;IAE3B,IAAIC,OAAO,GAAG,IAAI;IAElB,OAAO1B,KAAK,CAAC2B,IAAI,IAAIJ,cAAc,IAAIG,OAAO,EAAE;MAC9CL,KAAK,GAAGE,cAAc,CAACvB,KAAK,CAAC2B,IAAI,CAAC,CAACN,KAAK;MAExCI,gBAAgB,CAACG,IAAI,CAAC5B,KAAK,CAAC2B,IAAI,CAAC;MAEjC,IAAI3B,KAAK,CAAC6B,MAAM,EAAE;QAChB,MAAMtB,OAAO,GAAGgB,cAAc,CAACvB,KAAK,CAAC2B,IAAI,CAAC;QAE1C,MAAMG,aAAa,GAAGC,MAAM,CAACC,WAAW,CACtCD,MAAM,CAACE,OAAO,CAACjC,KAAK,CAAC6B,MAAM,CAAC,CACzBK,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAA8B;UAC9C,IAAIA,KAAK,KAAKC,SAAS,EAAE;YACvB,IAAI9B,OAAO,EAAE;cACX,MAAM+B,QAAQ,GAAG/B,OAAO,CAACc,KAAK,EAAEkB,IAAI,CACjCC,IAAI,IAAKA,IAAI,CAACC,KAAK,KAAKN,GAC3B,CAAC,EAAEG,QAAQ;cAEX,IAAIA,QAAQ,EAAE;gBACZ,OAAO,IAAI;cACb;YACF,CAAC,MAAM;cACL,OAAO,IAAI;YACb;UACF;UAEA,MAAMI,SAAS,GAAGnC,OAAO,EAAEmC,SAAS,GAAGP,GAAG,CAAC,IAAInB,MAAM;UAErD,OAAO,CAACmB,GAAG,EAAEO,SAAS,CAACN,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,CACDO,MAAM,CAAEC,KAAK,IAAKA,KAAK,IAAI,IAAI,CACpC,CAAC;QAED,IAAIvB,KAAK,EAAElB,MAAM,EAAE;UACjB4B,MAAM,CAACc,MAAM,CAACzB,SAAS,EAAEU,aAAa,CAAC;QACzC;QAEA,IAAIN,YAAY,KAAKxB,KAAK,EAAE;UAC1B;UACA;UACAsB,aAAa,GAAG;YAAE,GAAGQ;UAAc,CAAC;UAEpCT;UACE;UAAA,EACEyB,OAAO,CAAC,CAAC;YAAEL;UAAM,CAAC,KAAK;YACvB,IAAIA,KAAK,EAAE;cACT;cACA,IAAInB,aAAa,EAAE;gBACjB;gBACA,OAAOA,aAAa,CAACmB,KAAK,CAAC;cAC7B;YACF;UACF,CAAC,CAAC;QACN;MACF;;MAEA;MACA,IAAI,CAAClB,cAAc,CAACvB,KAAK,CAAC2B,IAAI,CAAC,CAACnB,OAAO,IAAIR,KAAK,CAACD,KAAK,KAAKsC,SAAS,EAAE;QACpEX,OAAO,GAAG,KAAK;MACjB,CAAC,MAAM;QACLzB,KAAK,GACH,OAAOD,KAAK,CAACD,KAAK,CAACE,KAAK,KAAK,QAAQ,GACjCD,KAAK,CAACD,KAAK,CAACE,KAAK,GACjBD,KAAK,CAACD,KAAK,CAACG,MAAM,CAACC,MAAM,GAAG,CAAC;QAEnC,MAAM4C,SAAS,GAAG/C,KAAK,CAACD,KAAK,CAACG,MAAM,CAACD,KAAK,CAAC;QAC3C,MAAM+C,YAAY,GAAGzB,cAAc,CAACvB,KAAK,CAAC2B,IAAI,CAAC,CAACnB,OAAO;;QAEvD;QACA,IAAIwC,YAAY,IAAID,SAAS,CAACpB,IAAI,IAAIqB,YAAY,EAAE;UAClDhD,KAAK,GAAG+C,SAA8C;UACtDxB,cAAc,GAAGyB,YAAY;QAC/B,CAAC,MAAM;UACL;UACAtB,OAAO,GAAG,KAAK;QACjB;MACF;IACF;IAEA,IAAIH,cAAc,CAACvB,KAAK,CAAC2B,IAAI,CAAC,KAAKU,SAAS,EAAE;MAC5CnB,IAAI,IAAIG,KAAK,EACTa,GAAG,CAAC,CAAC;QAAEe,OAAO;QAAER,KAAK;QAAEH;MAAS,CAAC,KAAK;QACtC;QACA;QACA;QACA,IAAIW,OAAO,KAAK,GAAG,EAAE;UACnB,OAAOjD,KAAK,CAAC2B,IAAI;QACnB;;QAEA;QACA,IAAIc,KAAK,EAAE;UACT,MAAML,KAAK,GAAGhB,SAAS,CAACqB,KAAK,CAAC;UAE9B,IAAIL,KAAK,KAAKC,SAAS,IAAIC,QAAQ,EAAE;YACnC;YACA,OAAO,EAAE;UACX;;UAEA;UACA;UACA,OAAOtB,MAAM,CAACoB,KAAK,CAAC,CAACc,OAAO,CAC1B,iCAAiC,EAChCC,IAAI,IAAKC,kBAAkB,CAACD,IAAI,CACnC,CAAC;QACH;QAEA,OAAOC,kBAAkB,CAACH,OAAO,CAAC;MACpC,CAAC,CAAC,CACDI,IAAI,CAAC,GAAG,CAAC;IACd,CAAC,MAAM;MACLnC,IAAI,IAAIkC,kBAAkB,CAACpD,KAAK,CAAC2B,IAAI,CAAC;IACxC;IAEA,IAAI,CAACL,aAAa,IAAIE,YAAY,CAACK,MAAM,EAAE;MACzCP,aAAa,GAAGS,MAAM,CAACC,WAAW,CAChCD,MAAM,CAACE,OAAO,CAACT,YAAY,CAACK,MAAM,CAAC,CAACK,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK,CACxDD,GAAG,EACHnB,MAAM,CAACoB,KAAK,CAAC,CACd,CACH,CAAC;IACH;IAEA,IAAIpC,KAAK,CAACD,KAAK,EAAE;MACfmB,IAAI,IAAI,GAAG;IACb,CAAC,MAAM,IAAII,aAAa,EAAE;MACxB,KAAK,MAAMmB,KAAK,IAAInB,aAAa,EAAE;QACjC,IAAIA,aAAa,CAACmB,KAAK,CAAC,KAAK,WAAW,EAAE;UACxC;UACA,OAAOnB,aAAa,CAACmB,KAAK,CAAC;QAC7B;MACF;MAEA,MAAMa,KAAK,GAAG3D,WAAW,CAAC+C,SAAS,CAACpB,aAAa,EAAE;QAAEiC,IAAI,EAAE;MAAM,CAAC,CAAC;MAEnE,IAAID,KAAK,EAAE;QACTpC,IAAI,IAAI,IAAIoC,KAAK,EAAE;MACrB;IACF;IAEAnC,OAAO,GAAGnB,KAAK,CAACD,KAAK;EACvB;;EAEA;EACA,IAAIQ,OAAO,EAAEW,IAAI,EAAE;IACjBA,IAAI,GAAG,GAAGX,OAAO,CAACW,IAAI,IAAIA,IAAI,EAAE;EAClC;;EAEA;EACAA,IAAI,GAAGA,IAAI,CAACgC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EAChChC,IAAI,GAAGA,IAAI,CAACf,MAAM,GAAG,CAAC,GAAGe,IAAI,CAACgC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAGhC,IAAI;;EAEvD;EACA;EACA,IAAI,CAACA,IAAI,CAACsC,UAAU,CAAC,GAAG,CAAC,EAAE;IACzBtC,IAAI,GAAG,IAAIA,IAAI,EAAE;EACnB;EAEA,OAAOA,IAAI;AACb;AAEA,MAAMuC,gBAAgB,GAAGA,CACvBC,MAAmC,EACnCC,WAA2B,KACZ;EACf,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;IAC9B;IACA,MAAMrC,KAAK,GAAGzB,eAAe,CAAC8D,MAAM,CAAC;IAErC,IAAIC,WAAW,EAAE;MACf,OAAO;QAAEtC,KAAK,EAAE,CAAC,GAAGsC,WAAW,EAAE,GAAGtC,KAAK;MAAE,CAAC;IAC9C;IAEA,OAAO;MAAEA;IAAM,CAAC;EAClB;EAEA,IAAIqC,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACxC,IAAI,KAAKmB,SAAS,EAAE;IAC7C,MAAM,IAAItB,KAAK,CACb,sJACF,CAAC;EACH;;EAEA;EACA;EACA,MAAMM,KAAK,GACTqC,MAAM,CAACE,KAAK,KAAK,IAAI,GACjB,CACE,IAAID,WAAW,IAAI,EAAE,CAAC,EACtB,IAAID,MAAM,CAACxC,IAAI,GAAGtB,eAAe,CAAC8D,MAAM,CAACxC,IAAI,CAAC,GAAG,EAAE,CAAC,CACrD,GACDwC,MAAM,CAACxC,IAAI,GACTtB,eAAe,CAAC8D,MAAM,CAACxC,IAAI,CAAC,GAC5BmB,SAAS;EAEjB,MAAM7B,OAAO,GAAGkD,MAAM,CAAClD,OAAO,GAC1BI,uBAAuB,CAAC8C,MAAM,CAAClD,OAAO,EAAEa,KAAK,CAAC,GAC9CgB,SAAS;EAEb,OAAO;IACLhB,KAAK;IACLqB,SAAS,EAAEgB,MAAM,CAAChB,SAAS;IAC3BlC;EACF,CAAC;AACH,CAAC;AAED,MAAMI,uBAAuB,GAAGA,CAC9BL,OAA8B,EAC9Bc,KAAqB,KAErBU,MAAM,CAACC,WAAW,CAChBD,MAAM,CAACE,OAAO,CAAC1B,OAAO,CAAC,CAAC2B,GAAG,CAAC,CAAC,CAACP,IAAI,EAAEkC,CAAC,CAAC,KAAK;EACzC,MAAMC,MAAM,GAAGL,gBAAgB,CAACI,CAAC,EAAExC,KAAK,CAAC;EAEzC,OAAO,CAACM,IAAI,EAAEmC,MAAM,CAAC;AACvB,CAAC,CACH,CAAC", "ignoreList": []}