{"version": 3, "names": ["React", "NavigationBuilderContext", "NavigationContext", "NavigationRouteContext", "SceneView", "ThemeContext", "useNavigationCache", "useRouteCache", "jsx", "_jsx", "useDescriptors", "state", "screens", "navigation", "screenOptions", "screenLayout", "onAction", "getState", "setState", "addListener", "addKeyedListener", "onRouteFocus", "router", "emitter", "theme", "useContext", "options", "setOptions", "useState", "onDispatchAction", "onOptionsChange", "scheduleUpdate", "flushUpdates", "stackRef", "context", "useMemo", "base", "navigations", "routes", "getOptions", "route", "overrides", "config", "name", "screen", "props", "optionsList", "filter", "Boolean", "reduce", "acc", "curr", "Object", "assign", "render", "customOptions", "routeState", "clearOptions", "o", "key", "_", "rest", "layout", "element", "children", "Provider", "value", "descriptors", "i", "describe", "placeholder", "Error", "undefined"], "sourceRoot": "../../src", "sources": ["useDescriptors.tsx"], "mappings": ";;AAOA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAGEC,wBAAwB,QACnB,+BAA4B;AACnC,SAASC,iBAAiB,QAAQ,wBAAqB;AACvD,SAASC,sBAAsB,QAAQ,6BAA0B;AACjE,SAASC,SAAS,QAAQ,gBAAa;AACvC,SAASC,YAAY,QAAQ,2BAAwB;AAUrD,SAASC,kBAAkB,QAAQ,yBAAsB;AACzD,SAASC,aAAa,QAAQ,oBAAiB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AA2DhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAK5B;EACAC,KAAK;EACLC,OAAO;EACPC,UAAU;EACVC,aAAa;EACbC,YAAY;EACZC,QAAQ;EACRC,QAAQ;EACRC,QAAQ;EACRC,WAAW;EACXC,gBAAgB;EAChBC,YAAY;EACZC,MAAM;EACNC;AACuC,CAAC,EAAE;EAC1C,MAAMC,KAAK,GAAGxB,KAAK,CAACyB,UAAU,CAACpB,YAAY,CAAC;EAC5C,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAG3B,KAAK,CAAC4B,QAAQ,CAC1C,CAAC,CACH,CAAC;EACD,MAAM;IACJC,gBAAgB;IAChBC,eAAe;IACfC,cAAc;IACdC,YAAY;IACZC;EACF,CAAC,GAAGjC,KAAK,CAACyB,UAAU,CAACxB,wBAAwB,CAAC;EAE9C,MAAMiC,OAAO,GAAGlC,KAAK,CAACmC,OAAO,CAC3B,OAAO;IACLtB,UAAU;IACVG,QAAQ;IACRG,WAAW;IACXC,gBAAgB;IAChBC,YAAY;IACZQ,gBAAgB;IAChBC,eAAe;IACfC,cAAc;IACdC,YAAY;IACZC;EACF,CAAC,CAAC,EACF,CACEpB,UAAU,EACVG,QAAQ,EACRG,WAAW,EACXC,gBAAgB,EAChBC,YAAY,EACZQ,gBAAgB,EAChBC,eAAe,EACfC,cAAc,EACdC,YAAY,EACZC,QAAQ,CAEZ,CAAC;EAED,MAAM;IAAEG,IAAI;IAAEC;EAAY,CAAC,GAAG/B,kBAAkB,CAK9C;IACAK,KAAK;IACLM,QAAQ;IACRJ,UAAU;IACVc,UAAU;IACVL,MAAM;IACNC;EACF,CAAC,CAAC;EAEF,MAAMe,MAAM,GAAG/B,aAAa,CAACI,KAAK,CAAC2B,MAAM,CAAC;EAE1C,MAAMC,UAAU,GAAGA,CACjBC,KAAuC,EACvC3B,UAOC,EACD4B,SAAwC,KACrC;IACH,MAAMC,MAAM,GAAG9B,OAAO,CAAC4B,KAAK,CAACG,IAAI,CAAC;IAClC,MAAMC,MAAM,GAAGF,MAAM,CAACG,KAAK;IAE3B,MAAMC,WAAW,GAAG;IAClB;IACAhC,aAAa;IACb;IACA,IAAK4B,MAAM,CAAChB,OAAO,GACfgB,MAAM,CAAChB,OAAO,CAACqB,MAAM,CAACC,OAAO,CAAC,GAC9B,EAAE,CAA8C;IACpD;IACAJ,MAAM,CAAClB,OAAO;IACd;IACAe,SAAS,CACV;IAED,OAAOK,WAAW,CAACG,MAAM,CACvB,CAACC,GAAG,EAAEC,IAAI,KACRC,MAAM,CAACC,MAAM,CACXH,GAAG;IACH;IACA,OAAOC,IAAI,KAAK,UAAU,GAAGA,IAAI,GAAGA,IAAI,CAAC;MAAEX,KAAK;MAAE3B,UAAU;MAAEW;IAAM,CAAC,CACvE,CAAC,EACH,CAAC,CACH,CAAC;EACH,CAAC;EAED,MAAM8B,MAAM,GAAGA,CACbd,KAAuC,EACvC3B,UAOC,EACD0C,aAA4B,EAC5BC,UAAuE,KACpE;IACH,MAAMd,MAAM,GAAG9B,OAAO,CAAC4B,KAAK,CAACG,IAAI,CAAC;IAClC,MAAMC,MAAM,GAAGF,MAAM,CAACG,KAAK;IAE3B,MAAMY,YAAY,GAAGA,CAAA,KACnB9B,UAAU,CAAE+B,CAAC,IAAK;MAChB,IAAIlB,KAAK,CAACmB,GAAG,IAAID,CAAC,EAAE;QAClB;QACA,MAAM;UAAE,CAAClB,KAAK,CAACmB,GAAG,GAAGC,CAAC;UAAE,GAAGC;QAAK,CAAC,GAAGH,CAAC;QACrC,OAAOG,IAAI;MACb;MAEA,OAAOH,CAAC;IACV,CAAC,CAAC;IAEJ,MAAMI,MAAM;IACV;IACAlB,MAAM,CAACkB,MAAM;IACb;IACApB,MAAM,CAACoB,MAAM;IACb;IACA/C,YAAY;IAEd,IAAIgD,OAAO,gBACTtD,IAAA,CAACL,SAAS;MACRS,UAAU,EAAEA,UAAW;MACvB2B,KAAK,EAAEA,KAAM;MACbI,MAAM,EAAEA,MAAO;MACfY,UAAU,EAAEA,UAAW;MACvBvC,QAAQ,EAAEA,QAAS;MACnBC,QAAQ,EAAEA,QAAS;MACnBQ,OAAO,EAAE6B,aAAc;MACvBE,YAAY,EAAEA;IAAa,CAC5B,CACF;IAED,IAAIK,MAAM,IAAI,IAAI,EAAE;MAClBC,OAAO,GAAGD,MAAM,CAAC;QACftB,KAAK;QACL3B,UAAU;QACVa,OAAO,EAAE6B,aAAa;QACtB;QACA/B,KAAK;QACLwC,QAAQ,EAAED;MACZ,CAAC,CAAC;IACJ;IAEA,oBACEtD,IAAA,CAACR,wBAAwB,CAACgE,QAAQ;MAAiBC,KAAK,EAAEhC,OAAQ;MAAA8B,QAAA,eAChEvD,IAAA,CAACP,iBAAiB,CAAC+D,QAAQ;QAACC,KAAK,EAAErD,UAAW;QAAAmD,QAAA,eAC5CvD,IAAA,CAACN,sBAAsB,CAAC8D,QAAQ;UAACC,KAAK,EAAE1B,KAAM;UAAAwB,QAAA,EAC3CD;QAAO,CACuB;MAAC,CACR;IAAC,GALSvB,KAAK,CAACmB,GAMX,CAAC;EAExC,CAAC;EAED,MAAMQ,WAAW,GAAG7B,MAAM,CAACW,MAAM,CAiB/B,CAACC,GAAG,EAAEV,KAAK,EAAE4B,CAAC,KAAK;IACnB,MAAMvD,UAAU,GAAGwB,WAAW,CAACG,KAAK,CAACmB,GAAG,CAAC;IACzC,MAAMJ,aAAa,GAAGhB,UAAU,CAACC,KAAK,EAAE3B,UAAU,EAAEa,OAAO,CAACc,KAAK,CAACmB,GAAG,CAAC,CAAC;IACvE,MAAMI,OAAO,GAAGT,MAAM,CACpBd,KAAK,EACL3B,UAAU,EACV0C,aAAa,EACb5C,KAAK,CAAC2B,MAAM,CAAC8B,CAAC,CAAC,CAACzD,KAClB,CAAC;IAEDuC,GAAG,CAACV,KAAK,CAACmB,GAAG,CAAC,GAAG;MACfnB,KAAK;MACL;MACA3B,UAAU;MACVyC,MAAMA,CAAA,EAAG;QACP,OAAOS,OAAO;MAChB,CAAC;MACDrC,OAAO,EAAE6B;IACX,CAAC;IAED,OAAOL,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEN;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMmB,QAAQ,GAAGA,CAAC7B,KAA+B,EAAE8B,WAAoB,KAAK;IAC1E,IAAI,CAACA,WAAW,EAAE;MAChB,IAAI,EAAE9B,KAAK,CAACmB,GAAG,IAAIQ,WAAW,CAAC,EAAE;QAC/B,MAAM,IAAII,KAAK,CAAC,sCAAsC/B,KAAK,CAACmB,GAAG,GAAG,CAAC;MACrE;MAEA,OAAOQ,WAAW,CAAC3B,KAAK,CAACmB,GAAG,CAAC;IAC/B;IAEA,MAAM9C,UAAU,GAAGuB,IAAI;IACvB,MAAMmB,aAAa,GAAGhB,UAAU,CAACC,KAAK,EAAE3B,UAAU,EAAE,CAAC,CAAC,CAAC;IACvD,MAAMkD,OAAO,GAAGT,MAAM,CAACd,KAAK,EAAE3B,UAAU,EAAE0C,aAAa,EAAEiB,SAAS,CAAC;IAEnE,OAAO;MACLhC,KAAK;MACL3B,UAAU;MACVyC,MAAMA,CAAA,EAAG;QACP,OAAOS,OAAO;MAChB,CAAC;MACDrC,OAAO,EAAE6B;IACX,CAAC;EACH,CAAC;EAED,OAAO;IACLc,QAAQ;IACRF;EACF,CAAC;AACH", "ignoreList": []}