/**
 * Library Screen
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { LibraryStackScreenProps } from '../navigation/types';

type Props = LibraryStackScreenProps<'LibraryHome'>;

const LibraryScreen: React.FC<Props> = ({ navigation }) => {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Your Library</Text>
        <Text style={styles.subtitle}>
          Manage your documents and reading materials
        </Text>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Documents</Text>
          <Text style={styles.placeholder}>
            No documents yet. Import your first document to get started.
          </Text>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Categories</Text>
          <Text style={styles.placeholder}>
            Organize your documents by category for easy access.
          </Text>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Supported Formats</Text>
          <Text style={styles.supportedFormat}>• PDF Documents</Text>
          <Text style={styles.supportedFormat}>• EPUB E-books</Text>
          <Text style={styles.supportedFormat}>• MOBI E-books</Text>
          <Text style={styles.supportedFormat}>• TXT Text Files</Text>
          <Text style={styles.supportedFormat}>• DOCX Documents</Text>
          <Text style={styles.supportedFormat}>• RTF Documents</Text>
          <Text style={styles.supportedFormat}>• HTML Files</Text>
          <Text style={styles.supportedFormat}>• Markdown Files</Text>
          <Text style={styles.supportedFormat}>• CBZ/CBR Comics</Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#757575',
    marginBottom: 32,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#424242',
    marginBottom: 16,
  },
  placeholder: {
    fontSize: 16,
    color: '#9E9E9E',
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 20,
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
  },
  supportedFormat: {
    fontSize: 16,
    color: '#424242',
    marginBottom: 8,
    paddingLeft: 16,
  },
});

export default LibraryScreen;
