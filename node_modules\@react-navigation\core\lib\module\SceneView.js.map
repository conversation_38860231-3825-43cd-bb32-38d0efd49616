{"version": 3, "names": ["React", "EnsureSingleNavigator", "NavigationFocusedRouteStateContext", "NavigationStateContext", "StaticContainer", "useOptionsGetters", "jsx", "_jsx", "SceneView", "screen", "route", "navigation", "routeState", "getState", "setState", "options", "clearOptions", "navigator<PERSON><PERSON><PERSON><PERSON>", "useRef", "undefined", "<PERSON><PERSON><PERSON>", "useCallback", "current", "addOptionsGetter", "key", "<PERSON><PERSON><PERSON>", "getCurrentState", "state", "currentRoute", "routes", "find", "r", "setCurrentState", "child", "map", "nextRoute", "params", "initial", "rest", "Object", "keys", "length", "isInitialRef", "useEffect", "getIsInitial", "parentFocusedRouteState", "useContext", "focusedRouteState", "useMemo", "name", "path", "addState", "parent", "parentRoute", "context", "ScreenComponent", "getComponent", "component", "Provider", "value", "children", "render"], "sourceRoot": "../../src", "sources": ["SceneView.tsx"], "mappings": ";;AAMA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,qBAAqB,QAAQ,4BAAyB;AAC/D,SAEEC,kCAAkC,QAC7B,yCAAsC;AAC7C,SAASC,sBAAsB,QAAQ,6BAA0B;AACjE,SAASC,eAAe,QAAQ,sBAAmB;AAEnD,SAASC,iBAAiB,QAAQ,wBAAqB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAmBxD;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAGvB;EACAC,MAAM;EACNC,KAAK;EACLC,UAAU;EACVC,UAAU;EACVC,QAAQ;EACRC,QAAQ;EACRC,OAAO;EACPC;AAC2B,CAAC,EAAE;EAC9B,MAAMC,eAAe,GAAGjB,KAAK,CAACkB,MAAM,CAAqBC,SAAS,CAAC;EACnE,MAAMC,MAAM,GAAGpB,KAAK,CAACqB,WAAW,CAAC,MAAMJ,eAAe,CAACK,OAAO,EAAE,EAAE,CAAC;EAEnE,MAAM;IAAEC;EAAiB,CAAC,GAAGlB,iBAAiB,CAAC;IAC7CmB,GAAG,EAAEd,KAAK,CAACc,GAAG;IACdT,OAAO;IACPJ;EACF,CAAC,CAAC;EAEF,MAAMc,MAAM,GAAGzB,KAAK,CAACqB,WAAW,CAAEG,GAAW,IAAK;IAChDP,eAAe,CAACK,OAAO,GAAGE,GAAG;EAC/B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,eAAe,GAAG1B,KAAK,CAACqB,WAAW,CAAC,MAAM;IAC9C,MAAMM,KAAK,GAAGd,QAAQ,CAAC,CAAC;IACxB,MAAMe,YAAY,GAAGD,KAAK,CAACE,MAAM,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACP,GAAG,KAAKd,KAAK,CAACc,GAAG,CAAC;IAElE,OAAOI,YAAY,GAAGA,YAAY,CAACD,KAAK,GAAGR,SAAS;EACtD,CAAC,EAAE,CAACN,QAAQ,EAAEH,KAAK,CAACc,GAAG,CAAC,CAAC;EAEzB,MAAMQ,eAAe,GAAGhC,KAAK,CAACqB,WAAW,CACtCY,KAAkE,IAAK;IACtE,MAAMN,KAAK,GAAGd,QAAQ,CAAC,CAAC;IAExBC,QAAQ,CAAC;MACP,GAAGa,KAAK;MACRE,MAAM,EAAEF,KAAK,CAACE,MAAM,CAACK,GAAG,CAAEH,CAAC,IAAK;QAC9B,IAAIA,CAAC,CAACP,GAAG,KAAKd,KAAK,CAACc,GAAG,EAAE;UACvB,OAAOO,CAAC;QACV;QAEA,MAAMI,SAAS,GAAG;UAAE,GAAGJ,CAAC;UAAEJ,KAAK,EAAEM;QAAM,CAAC;;QAExC;QACA;QACA,IACEE,SAAS,CAACC,MAAM,KACd,OAAO,IAAID,SAAS,CAACC,MAAM,IAC3B,OAAOD,SAAS,CAACC,MAAM,CAACT,KAAK,KAAK,QAAQ,IAC1CQ,SAAS,CAACC,MAAM,CAACT,KAAK,KAAK,IAAI,IAC9B,QAAQ,IAAIQ,SAAS,CAACC,MAAM,IAC3B,OAAOD,SAAS,CAACC,MAAM,CAAC3B,MAAM,KAAK,QAAS,CAAC,EACjD;UACA;UACA;UACA,MAAM;YAAEkB,KAAK;YAAElB,MAAM;YAAE2B,MAAM;YAAEC,OAAO;YAAE,GAAGC;UAAK,CAAC,GAC/CH,SAAS,CAACC,MAAM;UAElB,IAAIG,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC,CAACG,MAAM,EAAE;YAC5BN,SAAS,CAACC,MAAM,GAAGE,IAAI;UACzB,CAAC,MAAM;YACL,OAAOH,SAAS,CAACC,MAAM;UACzB;QACF;QAEA,OAAOD,SAAS;MAClB,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EACD,CAACtB,QAAQ,EAAEH,KAAK,CAACc,GAAG,EAAEV,QAAQ,CAChC,CAAC;EAED,MAAM4B,YAAY,GAAG1C,KAAK,CAACkB,MAAM,CAAC,IAAI,CAAC;EAEvClB,KAAK,CAAC2C,SAAS,CAAC,MAAM;IACpBD,YAAY,CAACpB,OAAO,GAAG,KAAK;EAC9B,CAAC,CAAC;;EAEF;EACAtB,KAAK,CAAC2C,SAAS,CAAC,MAAM;IACpB,OAAO3B,YAAY;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM4B,YAAY,GAAG5C,KAAK,CAACqB,WAAW,CAAC,MAAMqB,YAAY,CAACpB,OAAO,EAAE,EAAE,CAAC;EAEtE,MAAMuB,uBAAuB,GAAG7C,KAAK,CAAC8C,UAAU,CAC9C5C,kCACF,CAAC;EAED,MAAM6C,iBAAiB,GAAG/C,KAAK,CAACgD,OAAO,CAAC,MAAM;IAC5C,MAAMrB,KAAwB,GAAG;MAC/BE,MAAM,EAAE,CACN;QACEL,GAAG,EAAEd,KAAK,CAACc,GAAG;QACdyB,IAAI,EAAEvC,KAAK,CAACuC,IAAI;QAChBb,MAAM,EAAE1B,KAAK,CAAC0B,MAAM;QACpBc,IAAI,EAAExC,KAAK,CAACwC;MACd,CAAC;IAEL,CAAC;;IAED;IACA,MAAMC,QAAQ,GACZC,MAAqC,IACf;MACtB,MAAMC,WAAW,GAAGD,MAAM,EAAEvB,MAAM,CAAC,CAAC,CAAC;MAErC,IAAIwB,WAAW,EAAE;QACf,OAAO;UACLxB,MAAM,EAAE,CACN;YACE,GAAGwB,WAAW;YACd1B,KAAK,EAAEwB,QAAQ,CAACE,WAAW,CAAC1B,KAAK;UACnC,CAAC;QAEL,CAAC;MACH;MAEA,OAAOA,KAAK;IACd,CAAC;IAED,OAAOwB,QAAQ,CAACN,uBAAuB,CAAC;EAC1C,CAAC,EAAE,CACDA,uBAAuB,EACvBnC,KAAK,CAACc,GAAG,EACTd,KAAK,CAACuC,IAAI,EACVvC,KAAK,CAAC0B,MAAM,EACZ1B,KAAK,CAACwC,IAAI,CACX,CAAC;EAEF,MAAMI,OAAO,GAAGtD,KAAK,CAACgD,OAAO,CAC3B,OAAO;IACLrB,KAAK,EAAEf,UAAU;IACjBC,QAAQ,EAAEa,eAAe;IACzBZ,QAAQ,EAAEkB,eAAe;IACzBZ,MAAM;IACNK,MAAM;IACNmB,YAAY;IACZrB;EACF,CAAC,CAAC,EACF,CACEX,UAAU,EACVc,eAAe,EACfM,eAAe,EACfZ,MAAM,EACNK,MAAM,EACNmB,YAAY,EACZrB,gBAAgB,CAEpB,CAAC;EAED,MAAMgC,eAAe,GAAG9C,MAAM,CAAC+C,YAAY,GACvC/C,MAAM,CAAC+C,YAAY,CAAC,CAAC,GACrB/C,MAAM,CAACgD,SAAS;EAEpB,oBACElD,IAAA,CAACJ,sBAAsB,CAACuD,QAAQ;IAACC,KAAK,EAAEL,OAAQ;IAAAM,QAAA,eAC9CrD,IAAA,CAACL,kCAAkC,CAACwD,QAAQ;MAACC,KAAK,EAAEZ,iBAAkB;MAAAa,QAAA,eACpErD,IAAA,CAACN,qBAAqB;QAAA2D,QAAA,eACpBrD,IAAA,CAACH,eAAe;UACd6C,IAAI,EAAExC,MAAM,CAACwC,IAAK;UAClBY,MAAM,EAAEN,eAAe,IAAI9C,MAAM,CAACmD,QAAS;UAC3CjD,UAAU,EAAEA,UAAW;UACvBD,KAAK,EAAEA,KAAM;UAAAkD,QAAA,EAEZL,eAAe,KAAKpC,SAAS,gBAC5BZ,IAAA,CAACgD,eAAe;YAAC5C,UAAU,EAAEA,UAAW;YAACD,KAAK,EAAEA;UAAM,CAAE,CAAC,GACvDD,MAAM,CAACmD,QAAQ,KAAKzC,SAAS,GAC/BV,MAAM,CAACmD,QAAQ,CAAC;YAAEjD,UAAU;YAAED;UAAM,CAAC,CAAC,GACpC;QAAI,CACO;MAAC,CACG;IAAC,CACmB;EAAC,CACf,CAAC;AAEtC", "ignoreList": []}