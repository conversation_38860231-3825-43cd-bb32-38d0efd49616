{"version": 3, "names": ["React", "CurrentRenderContext", "useCurrentRender", "state", "navigation", "descriptors", "current", "useContext", "isFocused", "options", "routes", "index", "key"], "sourceRoot": "../../src", "sources": ["useCurrentRender.tsx"], "mappings": ";;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,oBAAoB,QAAQ,2BAAwB;AAiB7D;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAAC;EAAEC,KAAK;EAAEC,UAAU;EAAEC;AAAqB,CAAC,EAAE;EAC5E,MAAMC,OAAO,GAAGN,KAAK,CAACO,UAAU,CAACN,oBAAoB,CAAC;EAEtD,IAAIK,OAAO,IAAIF,UAAU,CAACI,SAAS,CAAC,CAAC,EAAE;IACrCF,OAAO,CAACG,OAAO,GAAGJ,WAAW,CAACF,KAAK,CAACO,MAAM,CAACP,KAAK,CAACQ,KAAK,CAAC,CAACC,GAAG,CAAC,CAACH,OAAO;EACtE;AACF", "ignoreList": []}