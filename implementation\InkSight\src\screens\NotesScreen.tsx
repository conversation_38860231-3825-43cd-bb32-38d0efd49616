/**
 * Notes Screen
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { NotesStackScreenProps } from '../navigation/types';

type Props = NotesStackScreenProps<'NotesHome'>;

const NotesScreen: React.FC<Props> = ({ navigation }) => {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Your Notes</Text>
        <Text style={styles.subtitle}>
          Handwritten and digital notes with AI recognition
        </Text>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Notes</Text>
          <Text style={styles.placeholder}>
            No notes yet. Create your first note to get started.
          </Text>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Handwriting Recognition</Text>
          <Text style={styles.feature}>
            • AI-powered handwriting recognition with 87% accuracy
          </Text>
          <Text style={styles.feature}>
            • Support for multiple languages
          </Text>
          <Text style={styles.feature}>
            • Real-time text conversion
          </Text>
          <Text style={styles.feature}>
            • Offline processing for privacy
          </Text>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Note Features</Text>
          <Text style={styles.feature}>
            • Rich text formatting
          </Text>
          <Text style={styles.feature}>
            • Document annotations
          </Text>
          <Text style={styles.feature}>
            • Search within notes
          </Text>
          <Text style={styles.feature}>
            • Export to multiple formats
          </Text>
          <Text style={styles.feature}>
            • Automatic backup and sync
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#757575',
    marginBottom: 32,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#424242',
    marginBottom: 16,
  },
  placeholder: {
    fontSize: 16,
    color: '#9E9E9E',
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 20,
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
  },
  feature: {
    fontSize: 16,
    color: '#424242',
    marginBottom: 8,
    paddingLeft: 16,
  },
});

export default NotesScreen;
